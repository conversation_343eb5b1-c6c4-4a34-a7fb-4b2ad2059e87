import React from 'react';
import {
  DatePicker as EverestDatePicker,
  type IDatePickerTextMap,
  type IDatePickerProps as IEverestDatePickerProps,
  type IDateTimeFormatPart,
  type IDateSegmentPlaceholder,
  type IWeekday,
  type TDate,
  type TCalendarView,
} from '@ceridianhcm/components';

const WEEKDAYS: IWeekday[] = [
  { short: 'Su', long: 'Sunday' },
  { short: 'Mo', long: 'Monday' },
  { short: 'Tu', long: 'Tuesday' },
  { short: 'We', long: 'Wednesday' },
  { short: 'Th', long: 'Thursday' },
  { short: 'Fr', long: 'Friday' },
  { short: 'Sa', long: 'Saturday' },
];

const MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

const DATE_SEGMENT_PLACEHOLDER: IDateSegmentPlaceholder = {
  month: 'MM',
  day: 'DD',
  year: 'YYYY',
};

const getDateTimeParts = (): IDateTimeFormatPart[] => {
  const parts = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).formatToParts(new Date());
  return parts.map((p, idx) => ({ id: `${p.type}-${idx}`, type: p.type, value: p.value }));
};

const TEXT_MAP: IDatePickerTextMap = {
  clearButtonAriaLabel: 'Clear',
  calendarAriaLabel: 'Calendar',
  dayLabel: 'Day',
  monthLabel: 'Month',
  yearLabel: 'Year',
  formatLabel: 'Date format',
  expectedDateFormatLabel: 'Expected date format',
  blank: 'Blank',
  nextMonthButtonLabel: 'Next month',
  previousMonthButtonLabel: 'Previous month',
  previousViewIsYearLabel: 'Year view',
  previousViewIsMonthLabel: 'Month view',
};

const formatDateForSR = (_view: TCalendarView, value?: TDate): string => {
  if (!value) return '';

  // Normalize the incoming value to a Date instance.
  //  • `number`  – epoch milliseconds
  //  • `string`  – any parsable date string
  //  • `Date`    – already a Date instance
  const dateObj: Date = typeof value === 'number' || typeof value === 'string' ? new Date(value) : (value as Date);

  // Guard against invalid dates which would otherwise throw when calling toLocaleDateString.
  return Number.isNaN(dateObj.getTime()) ? '' : dateObj.toLocaleDateString();
};

export interface IDatePickerProps {
  id: string;
  value?: Date;
  disabled?: boolean;
  required?: boolean;
  label?: string;
  onChange?: (date?: Date) => void;
}

export const DatePicker: React.FC<IDatePickerProps> = ({ id, value, disabled, required, label, onChange }) => {
  const handleChange: IEverestDatePickerProps['onChange'] = (_displayValue, newDate) => {
    onChange?.(newDate);
  };

  return (
    <EverestDatePicker
      id={id}
      value={value}
      disabled={disabled}
      required={required}
      label={label}
      dateTimeParts={React.useMemo(getDateTimeParts, [])}
      dateSegmentPlaceholder={DATE_SEGMENT_PLACEHOLDER}
      weekdays={WEEKDAYS}
      months={MONTHS}
      textMap={TEXT_MAP}
      formatDateForSR={formatDateForSR}
      onChange={handleChange}
    />
  );
};
