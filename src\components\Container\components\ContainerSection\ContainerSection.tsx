import React, { useContext } from 'react';
import classNames from 'classnames';
import { Divider } from '@ceridianhcm/components';
import { StackPanel } from '@ceridianhcm/everest-cdk';
import { ContainerSectionProps } from '../../types';
import { ContainerContext } from '../ContainerContext/ContainerContext';
import './container-section.scss';

export const ContainerSection: React.FC<ContainerSectionProps> = ({
  sectionTitle,
  children,
  className,
  divider = false,
  role = 'region',
  'aria-label': ariaLabel,
  isHorizontal = false,
  horizontalAlign = 'start',
}) => {
  const { baseId, title, breakpoint } = useContext(ContainerContext);

  if (!baseId) {
    throw new Error('ContainerSection must be used within a Container component');
  }
  const sectionClassNames = classNames(
    'container-section',
    {
      'with-divider': divider,
    },
    className,
  );

  const sectionContentClassName = classNames(
    'section-content',
    {
      'with-padding': breakpoint === 'xxl',
    },
    className,
  );
  const sectionTitleClassName = classNames('section-title', {
    'with-padding': breakpoint === 'xxl',
  });

  return (
    <>
      <div
        className={sectionClassNames}
        role={role}
        aria-label={ariaLabel || sectionTitle || title}
        data-testId={`${baseId}-section`}
      >
        {sectionTitle && <div className={sectionTitleClassName}>{sectionTitle}</div>}
        <StackPanel
          stackHorizontal={isHorizontal}
          horizontalAlign={horizontalAlign}
          className={sectionContentClassName}
        >
          {children}
        </StackPanel>
      </div>
      {divider && (
        <div className="section-divider">
          <Divider variant="lowEmp" testId={`${baseId}-divider`} id={`${baseId}-divider`}></Divider>
        </div>
      )}
    </>
  );
};
