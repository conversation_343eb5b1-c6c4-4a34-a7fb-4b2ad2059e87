import { z } from 'zod';

/**
 * Schema for comprehensive profile form validation
 * Demonstrates various field types and validation rules
 */
export const profileFormSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),

  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),

  email: z.string().email('Please enter a valid email address').max(100, 'Email must be less than 100 characters'),

  phoneNumber: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),

  biography: z.string().max(500, 'Biography must be less than 500 characters').optional().or(z.literal('')),

  startDate: z
    .date({
      required_error: 'Start date is required',
      invalid_type_error: 'Please enter a valid date',
    })
    .min(new Date('1900-01-01'), 'Start date must be after 1900')
    .max(new Date(), 'Start date cannot be in the future'),

  salary: z
    .number({
      required_error: 'Salary is required',
      invalid_type_error: 'Please enter a valid salary amount',
    })
    .min(0, 'Salary must be a positive number')
    .max(10000000, 'Salary must be less than 10,000,000'),

  yearsOfExperience: z
    .number({
      required_error: 'Years of experience is required',
      invalid_type_error: 'Please enter a valid number',
    })
    .int('Years of experience must be a whole number')
    .min(0, 'Years of experience cannot be negative')
    .max(70, 'Years of experience must be less than 70'),
});

export type ProfileFormData = z.infer<typeof profileFormSchema>;

// Default values for the form
export const defaultProfileFormValues: ProfileFormData = {
  firstName: '',
  lastName: '',
  email: '',
  phoneNumber: '',
  biography: '',
  startDate: new Date(),
  salary: 0,
  yearsOfExperience: 0,
};
