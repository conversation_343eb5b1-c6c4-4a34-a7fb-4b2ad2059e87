import React from 'react';
import { render, screen } from '@testing-library/react';
import { NotificationBanner } from './NotificationBanner';

describe('NotificationBanner Component', () => {
  const defaultProps = {
    id: 'test-notification',
    status: 'info' as const,
    children: 'Test notification message',
  };

  it('renders without crashing', () => {
    render(<NotificationBanner {...defaultProps} />);
    expect(screen.getByText('Test notification message')).toBeInTheDocument();
  });

  it('renders with different status types', () => {
    const statuses = ['success', 'info', 'warning', 'error'] as const;

    statuses.forEach((status) => {
      const { rerender } = render(<NotificationBanner {...defaultProps} status={status} />);

      expect(screen.getByText('Test notification message')).toBeInTheDocument();
      rerender(<div></div>);
    });
  });
});
