import { IEmployeeSkill } from '@models/qualifications';

export interface ILegacySkillTableVM {
  id: number;
  skill: string;
  skillLevel: string;
  expirationDate: string;
  dateLastAcquired: string;
  lastAssignedBy: string;
}

export const mapLegacySkillsToTableVM = (skills: IEmployeeSkill[]): ILegacySkillTableVM[] => {
  return skills.map((skill) => ({
    id: skill.Id,
    skill: skill.SkillName,
    skillLevel: skill.SkillLevelName,
    expirationDate: skill.ExpirationDate ? new Date(skill.ExpirationDate).toLocaleDateString() : 'N/A',
    dateLastAcquired: new Date(skill.DateLastAcquired).toLocaleDateString(),
    lastAssignedBy: skill.LastAssignedBy || 'Unknown',
  }));
};
