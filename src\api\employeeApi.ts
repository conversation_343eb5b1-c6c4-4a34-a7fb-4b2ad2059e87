import { handleApiRequest } from './baseApi';
import type { TApiResponse } from '../typings/api';
import { API_ENDPOINTS } from './apiConfig';

import type {
  TEmployeeProfileHeaderParams,
  TEmployeePositionDescriptionParams,
  TProfileImageFilteredUserIdsParams,
  TProfileImageParams,
  THexImage,
} from '../typings';

import type {
  IEmployeeBiographyResult,
  IEmployeeTypeRelationships,
  IEmployeeProfileHeaderResponse,
  IEmployeeProfilePronoun,
  IProfileImageUserIds,
} from '../models';

import {
  EmployeeProfileHeaderMockData,
  EmployeeBiographyMockData,
  EmployeeTypeRelationshipsMockData,
  EmployeePositionDescriptionMockData,
  EmployeePronounsMockData,
  ProfileImageUserIdsMockData,
  ProfileImageMockData,
} from '../mocks';

// GET

// POST
export const getEmployeeProfileHeader = async (
  payload: TEmployeeProfileHeaderParams,
): Promise<IEmployeeProfileHeaderResponse> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_PROFILE_HEADER,
    EmployeeProfileHeaderMockData,
    'Failed to fetch employee profile header.',
    payload,
  );
};

export const getEmployeeBiography = async (employeeId: number): Promise<TApiResponse<IEmployeeBiographyResult>> => {
  const sanitizedEmployeeId = encodeURIComponent(employeeId);
  const url = `${API_ENDPOINTS.EMPLOYEE_BIOGRAPHY}?employeeId=${sanitizedEmployeeId}`;

  return handleApiRequest('POST', url, EmployeeBiographyMockData, 'Failed to fetch employee biography.', null);
};

export const getEmployeeTypeRelationships = async (): Promise<TApiResponse<IEmployeeTypeRelationships[]>> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_TYPE_RELATIONSHIPS,
    EmployeeTypeRelationshipsMockData,
    'Failed to fetch employee relationship types.',
  );
};

export const getEmployeePositionDescription = async (payload: TEmployeePositionDescriptionParams): Promise<string> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_POSITION_DESCRIPTION,
    EmployeePositionDescriptionMockData,
    'Failed to fetch employee position description.',
    payload,
  );
};

export const getEmployeePronouns = async (): Promise<IEmployeeProfilePronoun[]> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_PRONOUNS,
    EmployeePronounsMockData,
    'Failed to fetch employee pronous.',
  );
};

export const getProfileImageFilteredUserIds = async (
  payload: TProfileImageFilteredUserIdsParams,
): Promise<IProfileImageUserIds> => {
  return handleApiRequest(
    'POST',
    API_ENDPOINTS.EMPLOYEE_PROFILE_IMAGE_FILTERED_USER_IDS,
    ProfileImageUserIdsMockData,
    'Failed to fetch profile image filtered userIds.',
    payload,
  );
};

export const getProfileImage = async (payload: TProfileImageParams): Promise<THexImage> => {
  return handleApiRequest(
    'GET',
    API_ENDPOINTS.EMPLOYEE_PROFILE_IMAGE,
    ProfileImageMockData,
    'Failed to fetch profile image',
    payload,
  );
};
export function getEmployeeProfileInfo():
  | TApiResponse<import('../features/about-me/components/PersonalInformation/models').IEmployeeProfileResult[]>
  | PromiseLike<
      TApiResponse<import('../features/about-me/components/PersonalInformation/models').IEmployeeProfileResult[]>
    > {
  throw new Error('Function not implemented.');
}
