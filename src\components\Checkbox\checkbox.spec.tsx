import { configure, fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { Checkbox, ICheckboxProps } from './Checkbox';

describe('Checkbox component', () => {
  beforeEach(() => {
    configure({ testIdAttribute: 'data-testid' });
  });

  it('should render in the DOM', () => {
    const checkedStateCallback = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: checkedStateCallback,
      checkedState: 'unchecked',
      testId: 'checkboxTestId',
    };
    render(<Checkbox {...props} />);
  });

  it('should display the label', () => {
    const checkedStateCallback = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: checkedStateCallback,
      checkedState: 'unchecked',
      testId: 'checkboxTestId',
      label: 'testLabel',
    };
    render(<Checkbox {...props} />);
    expect(screen.getByText('testLabel'));
  });

  it("should call the onCheckedStateChanged when the current state is 'unchecked' and is clicked", () => {
    const checkedStateCallback = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: checkedStateCallback,
      checkedState: 'unchecked',
      testId: 'checkboxTestId',
    };
    render(<Checkbox {...props} />);
    fireEvent.click(screen.getByTestId('checkboxTestId-checkbox'));
    expect(checkedStateCallback.mock.calls[0][0]).toBe('checked');
  });

  it("should call the onCheckedStateChanged when the current state is 'checked' and is clicked", () => {
    const checkedStateCallback = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: checkedStateCallback,
      checkedState: 'checked',
      testId: 'checkboxTestId',
    };
    render(<Checkbox {...props} />);
    fireEvent.click(screen.getByTestId('checkboxTestId-checkbox'));
    expect(checkedStateCallback.mock.calls[0][0]).toBe('unchecked');
  });

  it("shouldn't call the onCheckedStateChanged when the checkbox is disabled and clicked", () => {
    const checkedStateCallback = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: checkedStateCallback,
      checkedState: 'checked',
      testId: 'checkboxTestId',
      disabled: true,
    };
    render(<Checkbox {...props} />);
    fireEvent.click(screen.getByTestId('checkboxTestId-checkbox'));
    expect(checkedStateCallback).not.toHaveBeenCalled();
  });

  it("should display an error message when the checkbox's status is in error", () => {
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: () => {},
      checkedState: 'checked',
      testId: 'checkboxTestId',
      status: 'error',
      errorMessage: 'testErrorMsg',
    };
    render(<Checkbox {...props} />);
    expect(screen.getByText('testErrorMsg'));
  });

  it('should call the onclick function passed in when the checkbox is clicked', () => {
    const mockedOnClick = jest.fn();
    const props: ICheckboxProps = {
      id: 'checkboxTest',
      onCheckedStateChanged: () => {},
      checkedState: 'checked',
      testId: 'checkboxTestId',
      onChange: mockedOnClick,
    };
    render(<Checkbox {...props} />);
    fireEvent.click(screen.getByTestId('checkboxTestId-checkbox'));
    expect(mockedOnClick).toBeCalled();
  });
});
