import React, { useState } from 'react';
import { Tab, TabGroup } from '@components/Tab';
import { useTab } from '@components/Tab/hooks';
import { Table } from '@components/Table';
import { useStore } from '@hooks/useStore';
import { useFetchConfig } from '@hooks/useFetchConfig';
import { PreferencesStore } from '@store/preferencesStore';
import type { AppUserDelegate, AppUserDelegatesResponse } from '@models/preferences/appUserDelegates';
import { DefaultTextCell } from '@components/Table/cells';
import { NameCell } from './NameCell';
import { EffectivePeriodCell } from './EffectivePeriodCell';
import { RestrictCell } from './RestrictCell';

enum TabType {
  ActiveTab = 'active-delegations-tab',
  HistoryTab = 'delegations-history-tab',
}

const columnDefinitions = [
  {
    field: 'DisplayName',
    label: 'Name',
    sortable: true,
    minWidth: 200,
    cellTemplate: NameCell,
  },
  {
    field: 'EffectiveStart',
    label: 'Effective Period',
    sortable: false,
    minWidth: 220,
    cellTemplate: EffectivePeriodCell,
  },
  {
    field: 'Reason',
    label: 'Delegation Reason',
    sortable: true,
    minWidth: 180,
    cellTemplate: DefaultTextCell,
  },
  {
    field: 'RestrictPayAccessString',
    label: 'Restrict Information',
    sortable: false,
    minWidth: 220,
    cellTemplate: RestrictCell,
  },
];

export const DelegationAccessTabGroup: React.FC = () => {
  const prefStore = useStore(PreferencesStore);
  const [delegatesData, setDelegatesData] = useState<AppUserDelegatesResponse | null>(null);

  const fetchConfig = {
    appUserDelegates: {
      fetch: prefStore.fetchAppUserDelegates,
      onSuccess: (res: AppUserDelegatesResponse) => setDelegatesData(res),
      errorMessage: 'Failed to fetch app user delegates',
    },
  };

  useFetchConfig(fetchConfig);

  const Active = delegatesData?.Active || [];
  const History = delegatesData?.History || [];

  const { currentTab, handleTabChange } = useTab<TabType>(TabType.ActiveTab);

  const onTabChange = (newTabId: string) => {
    if (newTabId) {
      handleTabChange(newTabId);
    }
  };

  return (
    <div className="delegation-access-tab-group-wrapper">
      <TabGroup
        id="delegation-access-tab-group"
        activeId={currentTab}
        onActiveIdChange={onTabChange}
        textMap={{
          overflowButtonNextAriaLabel: 'Scroll to next',
          overflowButtonPrevAriaLabel: 'Scroll to previous',
          viewAllAriaLabel: 'View All Tabs',
        }}
      >
        <Tab id={TabType.ActiveTab} label="Active">
          {currentTab === TabType.ActiveTab && (
            <Table<AppUserDelegate>
              id="active-delegations-table"
              ariaLabel="Active Delegations Table"
              data={Active}
              rowsPerPage={5}
              hasTablePagination
              columnDefinitions={columnDefinitions}
            />
          )}
        </Tab>

        <Tab id={TabType.HistoryTab} label="History">
          {currentTab === TabType.HistoryTab && (
            <Table<AppUserDelegate>
              id="delegations-history-table"
              ariaLabel="Delegations History Table"
              data={History}
              rowsPerPage={5}
              hasTablePagination
              columnDefinitions={columnDefinitions}
            />
          )}
        </Tab>
      </TabGroup>
    </div>
  );
};
