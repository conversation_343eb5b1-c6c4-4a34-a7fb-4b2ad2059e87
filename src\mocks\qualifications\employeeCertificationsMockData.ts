import { IEmployeeCertification } from '@models/qualifications';

export const EmployeeCertificationsMockData: IEmployeeCertification[] = [
  {
    Id: 1,
    LMSCertificationId: 3,
    LMSEmployeeCertificationId: 94,
    EmployeeId: 1046,
    DateFirstAcquired: '2023-07-06T00:00:00',
    DateLastAcquired: '2023-07-06T00:00:00',
    ExpirationDate: '2025-07-01T23:59:00',
    DaysUntilExpiration: 42,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: '<PERSON>',
    LMSAssignmentMethodId: 7,
    CertificationAssignmentMethodCode: 'U',
    HasRequiredCourses: false,
    CertificationShortName: 'Customer Service',
    LMSEmployeeCertifications: null,
    LMSCourseCertificationId: null,
    LMSLearningPlanCertificationId: null,
    LMSCertificationStatus: 'Approved',
    LMSCertificationStatusId: 2,
    LMSCertificationStatusXRefCode: 'APPROVED',
    CertificationExpirationThreshold: 7,
    NumberOfNotes: null,
    IsExternal: false,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 2,
    LMSCertificationId: 1,
    LMSEmployeeCertificationId: 18,
    EmployeeId: 1046,
    DateFirstAcquired: '2019-08-15T00:00:00',
    DateLastAcquired: '2021-01-04T00:00:00',
    ExpirationDate: '2023-01-04T23:59:00',
    DaysUntilExpiration: -867,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    CertificationAssignmentMethodCode: 'U',
    HasRequiredCourses: false,
    CertificationShortName: 'First Aid',
    LMSEmployeeCertifications: null,
    LMSCourseCertificationId: null,
    LMSLearningPlanCertificationId: null,
    LMSCertificationStatus: 'Approved',
    LMSCertificationStatusId: 2,
    LMSCertificationStatusXRefCode: 'APPROVED',
    CertificationExpirationThreshold: 7,
    NumberOfNotes: null,
    IsExternal: false,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 3,
    LMSCertificationId: 5,
    LMSEmployeeCertificationId: 17,
    EmployeeId: 1046,
    DateFirstAcquired: '2020-01-06T00:00:00',
    DateLastAcquired: '2020-01-06T00:00:00',
    ExpirationDate: '2022-01-06T23:59:00',
    DaysUntilExpiration: -1230,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    CertificationAssignmentMethodCode: 'U',
    HasRequiredCourses: false,
    CertificationShortName: 'OSHA Training',
    LMSEmployeeCertifications: null,
    LMSCourseCertificationId: null,
    LMSLearningPlanCertificationId: null,
    LMSCertificationStatus: 'Approved',
    LMSCertificationStatusId: 2,
    LMSCertificationStatusXRefCode: 'APPROVED',
    CertificationExpirationThreshold: 7,
    NumberOfNotes: null,
    IsExternal: false,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 4,
    LMSCertificationId: 2,
    LMSEmployeeCertificationId: 5,
    EmployeeId: 1046,
    DateFirstAcquired: '2015-05-10T00:00:00',
    DateLastAcquired: '2015-05-10T00:00:00',
    ExpirationDate: null,
    DaysUntilExpiration: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    CertificationAssignmentMethodCode: 'U',
    HasRequiredCourses: false,
    CertificationShortName: 'Privacy Training',
    LMSEmployeeCertifications: null,
    LMSCourseCertificationId: null,
    LMSLearningPlanCertificationId: null,
    LMSCertificationStatus: 'Approved',
    LMSCertificationStatusId: 2,
    LMSCertificationStatusXRefCode: 'APPROVED',
    CertificationExpirationThreshold: 7,
    NumberOfNotes: null,
    IsExternal: false,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
];
