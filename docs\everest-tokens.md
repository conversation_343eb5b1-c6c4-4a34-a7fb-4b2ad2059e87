# Everest Design Tokens Guide

## Overview
Design tokens define the visual characteristics of a brand or a product such as typography, colors, and spacing. This guide outlines the Everest tokens to be used in the HR Profile UI microfrontend.

## Colors

Use colors consistently to convey visual hierarchy for users. Proper use of layering, color tone, and contrast can showcase quality of design craftsmanship.

### Usage Guidelines

```scss
// ✅ Do this
color: var(--evr-interactive-primary-default); // Use alias tokens

// ❌ Don't do this
color: var(--evr-primitive-neutral-100);
```

> **Warning**: Always use alias tokens instead of primitive tokens as they apply semantic meaning and are more future-proof.

### Alias Colors

These tokens apply semantic meaning and should be used instead of primitives.

| Category | Token | Usage |
|----------|-------|-------|
| **Interactive Primary** | `--evr-interactive-primary-default` | Default state for primary actions |
| | `--evr-interactive-primary-hovered` | Hover state for primary actions |
| | `--evr-interactive-primary-pressed` | Pressed state for primary actions |
| | `--evr-interactive-primary-decorative` | Decorative elements for primary actions |
| | `--evr-interactive-primary-focus` | Focus state for primary actions |
| **Status** | `--evr-interactive-status-error-default` | Default state for error indicators |
| | `--evr-interactive-status-warning-default` | Default state for warning indicators |
| | `--evr-interactive-status-success-default` | Default state for success indicators |
| **Content** | `--evr-content-primary-highemp` | High emphasis text |
| | `--evr-content-primary-default` | Default text |
| | `--evr-content-primary-lowemp` | Low emphasis text |
| | `--evr-content-primary-inverse` | Text on dark backgrounds |
| **Surfaces** | `--evr-surfaces-primary-default` | Primary background |
| | `--evr-surfaces-primary-hovered` | Hover state for primary background |
| | `--evr-surfaces-primary-selected` | Selected state for primary background |
| **Borders** | `--evr-borders-primary-default` | Default borders |
| | `--evr-borders-decorative-default` | Decorative borders |
| | `--evr-borders-status-error` | Error state borders |

## Typography

> **Warning**: The Everest rem tokens are based on a root font size of 16px (1rem = 16px). However, the Dayforce monolith has a root font size of 10px, so a multiplier of 1.6 is applied when Everest tokens are used within the monolith.

### Font Size

| Token | Value (1rem = 16px) | Dayforce Value (1rem = 10px) |
|-------|---------------------|------------------------------|
| `--evr-font-size-xs` | 0.625rem | 1rem |
| `--evr-font-size-sm` | 0.75rem | 1.2rem |
| `--evr-font-size-md` | 0.875rem | 1.4rem |
| `--evr-font-size-lg` | 1rem | 1.6rem |
| `--evr-font-size-xl` | 1.125rem | 1.8rem |
| `--evr-font-size-2xl` | 1.25rem | 2rem |
| `--evr-font-size-3xl` | 1.5rem | 2.4rem |

### Font Weight

| Token | Value |
|-------|-------|
| `--evr-regular-weight` | 400 |
| `--evr-medium-weight` | 500 |
| `--evr-demi-bold-weight` | 600 |
| `--evr-bold-weight` | 700 |

### Line Height

| Token | Value |
|-------|-------|
| `--evr-line-height-auto` | auto |
| `--evr-line-height-sm` | 100% |
| `--evr-line-height-md` | 125% |
| `--evr-line-height-lg` | 150% |

## Spacing

| Token | Value (1rem = 16px) | Dayforce Value (1rem = 10px) |
|-------|---------------------|------------------------------|
| `--evr-spacing-4xs` | 0.125rem | 0.2rem |
| `--evr-spacing-3xs` | 0.25rem | 0.4rem |
| `--evr-spacing-2xs` | 0.5rem | 0.8rem |
| `--evr-spacing-xs` | 0.75rem | 1.2rem |
| `--evr-spacing-sm` | 1rem | 1.6rem |
| `--evr-spacing-md` | 1.5rem | 2.4rem |
| `--evr-spacing-lg` | 2rem | 3.2rem |
| `--evr-spacing-xl` | 2.5rem | 4rem |
| `--evr-spacing-2xl` | 3rem | 4.8rem |

## Border Radius

| Token | Value (1rem = 16px) | Dayforce Value (1rem = 10px) |
|-------|---------------------|------------------------------|
| `--evr-radius-4xs` | 0.125rem | 0.2rem |
| `--evr-radius-3xs` | 0.25rem | 0.4rem |
| `--evr-radius-2xs` | 0.5rem | 0.8rem |
| `--evr-radius-xs` | 0.75rem | 1.2rem |
| `--evr-radius-sm` | 1rem | 1.6rem |
| `--evr-radius-circle` | 100% | 100% |

## Border Width

| Token | Value | Usage |
|-------|-------|-------|
| `--evr-border-width-thin-px` | 1px | Standard border (preferred) |
| `--evr-border-width-thin-rem` | 0.0625rem | For graphical components that scale with font size |
| `--evr-border-width-thick-px` | 2px | For emphasis (e.g., error states) |

## Elevation

| Token | Usage |
|-------|-------|
| `--evr-depth-02` | Subtle elevation for cards and containers |
| `--evr-depth-04` | Medium elevation for popovers and dropdowns |
| `--evr-depth-06` | High elevation for modals and dialogs |

## Focus Ring

| Token | Value |
|-------|-------|
| `--evr-focus-ring-radius` | 4px |
| `--evr-focus-ring-border` | 2px |
| `--evr-focus-ring-outline-offset` | 1px |
| `--evr-focus-ring-color` | var(--evr-interactive-primary-focus) |

## Z-index

| Token | Value |
|-------|-------|
| `--evr-z-index-inner` | 10 |
| `--evr-z-index-modal` | 1000 |
| `--evr-z-index-tooltip` | 1200 |