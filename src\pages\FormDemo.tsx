import React, { useState } from 'react';
import { Container } from '@components/Container';
import { SidePanel } from '@components/SidePanel';
import { Button } from '@ceridianhcm/components';
import { EditProfileForm } from '@features/about-me/components/EditProfileForm/EditProfileForm';

import { EditProfileFormData } from '@features/about-me/schemas/editProfileFormSchema';
import { editProfileFormMockData } from '@mocks/forms/editProfileFormMockData';

/**
 * FormDemo page demonstrates React Hook Form integration with HR Profile UI components.
 * This page showcases various form patterns and integrations.
 */
export const FormDemo: React.FC = () => {
  const [editProfileFormOpen, setEditProfileFormOpen] = useState(false);
  const [currentBiography, setCurrentBiography] = useState(editProfileFormMockData.biography || '');

  const handleEditProfileSubmit = (data: EditProfileFormData) => {
    console.log('Edit profile form submitted:', data);
    setCurrentBiography(data.biography || '');
    setEditProfileFormOpen(false);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>React Hook Form Integration Demo</h1>
      <p>This page demonstrates the React Hook Form integration with HR Profile UI components.</p>

      {/* Edit Profile Form Demo */}
      <div style={{ marginBottom: '100px' }}>
        <h2>Edit Profile Form Demo</h2>
        <div style={{ display: 'flex', gap: '16px', marginBottom: '16px' }}>
          <Button
            id="open-edit-profile-form"
            label="Open Edit Profile Form"
            onClick={() => setEditProfileFormOpen(true)}
            variant="secondary"
          />
        </div>
      </div>

      {/* Current Data Display */}
      <div style={{ marginBottom: '32px' }}>
        <h2>Current Form Data</h2>
        <Container id="current-data-container" title="Form Data Preview" ariaLabel="Current form data display">
          <Container.Body>
            <div>
              <h3>Biography</h3>
              <p
                style={{
                  padding: '12px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px',
                  minHeight: '60px',
                  fontStyle: currentBiography ? 'normal' : 'italic',
                  color: currentBiography ? '#333' : '#666',
                }}
              >
                {currentBiography || 'No biography provided yet.'}
              </p>
            </div>
          </Container.Body>
        </Container>
      </div>

      {/* Edit Profile Form SidePanel */}
      <SidePanel
        id="demo-edit-profile-form-panel"
        open={editProfileFormOpen}
        onClose={() => setEditProfileFormOpen(false)}
        title="Edit Profile"
        size="lg"
        testId="demo-edit-profile-form-sidepanel"
      >
        <EditProfileForm
          initialData={{
            biography: currentBiography,
            pronouns: editProfileFormMockData.pronouns,
            profilePhoto: editProfileFormMockData.profilePhoto,
          }}
          onSubmit={handleEditProfileSubmit}
          onCancel={() => setEditProfileFormOpen(false)}
          testId="demo-edit-profile-form"
        />
      </SidePanel>
    </div>
  );
};

export default FormDemo;
