import React from 'react';
import { Image } from '@ceridianhcm/components';
import { SectionPanel, SectionPanelRow } from '@ceridianhcm/components';
import { Container } from '@components';
import { FullWidthWrapper } from '@components/FullWidthWrapper';

import './single-letter-content.scss';

export interface SingleLetterContentProps {
  id: string;
  testId: string;
  sanitizedHtml: string;
}

export const SingleLetterContent: React.FC<SingleLetterContentProps> = ({ id, testId, sanitizedHtml }) => {
  return (
    <FullWidthWrapper testId={`${id}-container-panel-wrapper`} alignItems="center" minWidth="880px" maxWidth="880px">
      <Container id={`${id}-container-panel`} ariaLabel="Single Letter Content">
        <Container.Body testId={`${testId}-container-body`} className="single-letter-content-container">
          <FullWidthWrapper
            testId={`${id}-content-section-panel-wrapper`}
            alignItems="start"
            minWidth="720px"
            maxWidth="720px"
            verticalPadding="2xl"
            horizontalPadding="2xl"
          >
            <SectionPanel id={`${id}-content-section-panel`} testId={`${testId}-content-section-panel`}>
              <SectionPanelRow
                id={`${id}-company-logo-section-panel-row`}
                testId={`${testId}-company-logo-section-panel`}
              >
                <div className="company-logo-wrapper">
                  <Image id="dayforce-logo" src="/assets/images/dayforce-logo.svg" alt="dayforce logo" />
                </div>
              </SectionPanelRow>
              <SectionPanelRow id={`${id}-content-section-panel-row`} testId={`${testId}-content-section-panel`}>
                <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} data-testid={`${testId}-letter-content`} />
              </SectionPanelRow>
            </SectionPanel>
          </FullWidthWrapper>
        </Container.Body>
      </Container>
    </FullWidthWrapper>
  );
};
