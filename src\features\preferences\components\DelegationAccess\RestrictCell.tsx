import React from 'react';
import { TableCellLayout } from '@ceridianhcm/components';

export const RestrictCell = (cellContext: any) => {
  const {
    RestrictPayAccessString,
    RestrictCompensationAccessString,
    RestrictPerformanceAccessString,
    RestrictSuccessionAccessString,
    RestrictPiiDocumentAccessString,
  } = cellContext.rowData;

  const restrictions = [
    RestrictPayAccessString,
    RestrictCompensationAccessString,
    RestrictPerformanceAccessString,
    RestrictSuccessionAccessString,
    RestrictPiiDocumentAccessString,
  ].filter(Boolean);

  return (
    <TableCellLayout flexDirection="row" flexGap>
      <span className="cell-text-dark">{restrictions.join(', ')}</span>
    </TableCellLayout>
  );
};
