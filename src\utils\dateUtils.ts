/**
 * Formats a date string to a localized date format
 * @param dateString - The date string to format
 * @param locale - The locale to use for formatting (defaults to 'en-US')
 * @returns Formatted date string in the format "MMM DD, YYYY" or 'Invalid Date' if invalid
 */
export const formatDate = (dateString: string, locale: string = 'en-US'): string => {
  if (!dateString) {
    return 'Invalid Date';
  }

  const date = new Date(dateString);

  // Check for invalid date (NaN timestamp)
  if (Number.isNaN(date.getTime())) {
    return 'Invalid Date';
  }

  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};
