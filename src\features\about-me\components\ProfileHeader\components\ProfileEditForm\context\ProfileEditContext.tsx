import React, { createContext, useContext, ReactNode } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ProfileEditFormData } from '../profile-edit-form-schema';
import { useProfileEditForm } from '../hooks/useProfileEditForm';
import { ProfileEditStoreState, ProfileEditStoreActions } from '../store/profile-edit.store';

interface ProfileEditContextType {
  state: ProfileEditStoreState;
  methods?: UseFormReturn<ProfileEditFormData>;
  actions: ProfileEditStoreActions;
}

const ProfileEditContext = createContext<ProfileEditContextType | undefined>(undefined);

interface ProfileEditProviderProps {
  children: ReactNode;
  initialData: ProfileEditFormData;
}

export const ProfileEditProvider: React.FC<ProfileEditProviderProps> = ({ children, initialData }) => {
  const { state, actions } = useProfileEditForm();
  
  // Initialize store with initial data if needed
  React.useEffect(() => {
    if (initialData.pronouns) {
      actions.setPronounChoice?.(initialData.pronouns);
    }
    if (initialData.biography) {
      actions.setBiography?.(initialData.biography);
    }
  }, [initialData, actions]);

  const contextValue: ProfileEditContextType = {
    state,
    actions,
  };

  return <ProfileEditContext.Provider value={contextValue}>{children}</ProfileEditContext.Provider>;
};

export const useProfileEditContext = (): ProfileEditContextType => {
  const context = useContext(ProfileEditContext);
  if (!context) {
    throw new Error('useProfileEditContext must be used within a ProfileEditProvider');
  }
  return context;
};