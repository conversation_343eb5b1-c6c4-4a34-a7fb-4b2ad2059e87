lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@ceridianhcm/analytics':
        specifier: ^1.32.2
        version: 1.33.1
      '@ceridianhcm/components':
        specifier: ^1.43.113
        version: 1.43.123(@platform/core@1.33.1)(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/everest-cdk':
        specifier: ^1.43.21
        version: 1.43.21(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/everest-community':
        specifier: ^1.43.0
        version: 1.44.3(20e46a7e59d8ce1ac6f9a67ab650e1c7)
      '@ceridianhcm/globalization':
        specifier: ^1.32.5
        version: 1.33.1
      '@ceridianhcm/platform-df-assets':
        specifier: ^1.33.2
        version: 1.33.7
      '@ceridianhcm/react-core':
        specifier: ^1.32.8
        version: 1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/theme':
        specifier: ^1.43.1
        version: 1.43.1
      '@platform/catalog':
        specifier: ^1.32.9
        version: 1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
      '@platform/component':
        specifier: ^1.32.9
        version: 1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
      '@platform/core':
        specifier: ^1.27
        version: 1.33.1
      '@platform/dayforce':
        specifier: ^1.32.9
        version: 1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
      '@platform/theme':
        specifier: ^1.32.1
        version: 1.33.1
      classnames:
        specifier: ^2.5.1
        version: 2.5.1
      react:
        specifier: 17.0.1
        version: 17.0.1
      react-dom:
        specifier: 17.0.1
        version: 17.0.1(react@17.0.1)
      react-router-dom:
        specifier: ^6.30.0
        version: 6.30.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      xss:
        specifier: ^1.0.15
        version: 1.0.15
    devDependencies:
      '@ceridianhcm/eslint-config-platform':
        specifier: ^1.27.16
        version: 1.33.1(48c13056b39a799e5d1b3b0fb4ac1e8e)
      '@ceridianhcm/microfrontend-harness':
        specifier: ^1.10.37
        version: 1.33.1(@ceridianhcm/feature-flag-client@1.30.12)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(@react-rxjs/utils@0.9.7(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(react@17.0.1)(rxjs@7.8.2))
      '@pmmmwh/react-refresh-webpack-plugin':
        specifier: ^0.5.4
        version: 0.5.16(@types/webpack@4.41.40)(react-refresh@0.11.0)(type-fest@4.41.0)(webpack-dev-server@4.15.2)(webpack@5.99.9)
      '@testing-library/jest-dom':
        specifier: ^6.6.3
        version: 6.6.3
      '@testing-library/react':
        specifier: ^11.2.6
        version: 11.2.7(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@testing-library/react-hooks':
        specifier: ^8.0.1
        version: 8.0.1(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@testing-library/user-event':
        specifier: ^14.4.3
        version: 14.6.1(@testing-library/dom@7.31.2)
      '@types/dayforce-common':
        specifier: ^1.26.3
        version: 1.26.10
      '@types/dayforce-fallback':
        specifier: ^1.26.5
        version: 1.26.10
      '@types/jest':
        specifier: ^29.5.11
        version: 29.5.14
      '@types/jest-axe':
        specifier: ^3.5.9
        version: 3.5.9
      '@types/node':
        specifier: ^22.15.21
        version: 22.15.21
      '@types/react':
        specifier: 17.0.1
        version: 17.0.1
      '@types/react-dom':
        specifier: 17.0.1
        version: 17.0.1
      '@types/react-router-dom':
        specifier: ^5.3.3
        version: 5.3.3
      '@types/react-test-renderer':
        specifier: 17.0.1
        version: 17.0.1
      '@typescript-eslint/eslint-plugin':
        specifier: ^6.13.2
        version: 6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/parser':
        specifier: ^6.13.2
        version: 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      clean-webpack-plugin:
        specifier: ^3.0.0
        version: 3.0.0(webpack@5.99.9)
      copy-webpack-plugin:
        specifier: ^8.1.1
        version: 8.1.1(webpack@5.99.9)
      css-loader:
        specifier: ^5.2.4
        version: 5.2.7(webpack@5.99.9)
      dotenv:
        specifier: ^16.5.0
        version: 16.5.0
      eslint:
        specifier: ^8.54.0
        version: 8.57.1
      eslint-config-airbnb:
        specifier: ^19.0.4
        version: 19.0.4(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1))(eslint-plugin-react-hooks@4.6.2(eslint@8.57.1))(eslint-plugin-react@7.37.5(eslint@8.57.1))(eslint@8.57.1)
      eslint-config-airbnb-typescript:
        specifier: ^17.1.0
        version: 17.1.0(@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3))(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1)
      eslint-config-prettier:
        specifier: ^10.1.2
        version: 10.1.5(eslint@8.57.1)
      eslint-plugin-filename-rules:
        specifier: ^1.3.1
        version: 1.3.1
      eslint-plugin-html:
        specifier: ^7.1.0
        version: 7.1.0
      eslint-plugin-import:
        specifier: ^2.29.0
        version: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)
      eslint-plugin-jsx-a11y:
        specifier: ^6.8.0
        version: 6.10.2(eslint@8.57.1)
      eslint-plugin-react:
        specifier: ^7.33.2
        version: 7.37.5(eslint@8.57.1)
      eslint-plugin-react-hooks:
        specifier: ^4.6.0
        version: 4.6.2(eslint@8.57.1)
      eslint-plugin-testing-library:
        specifier: ^6.2.0
        version: 6.5.0(eslint@8.57.1)(typescript@5.8.3)
      file-loader:
        specifier: ^6.2.0
        version: 6.2.0(webpack@5.99.9)
      html-webpack-plugin:
        specifier: ^5.3.1
        version: 5.6.3(webpack@5.99.9)
      http-proxy-middleware:
        specifier: ^2.0.1
        version: 2.0.9(@types/express@4.17.22)
      http-server:
        specifier: ^0.12.3
        version: 0.12.3
      husky:
        specifier: ^7.0.4
        version: 7.0.4
      identity-obj-proxy:
        specifier: ^3.0.0
        version: 3.0.0
      jest:
        specifier: ^29.7.0
        version: 29.7.0(@types/node@22.15.21)
      jest-axe:
        specifier: ^9.0.0
        version: 9.0.0
      jest-environment-jsdom:
        specifier: ^29.7.0
        version: 29.7.0
      jest-junit:
        specifier: ^13.0.0
        version: 13.2.0
      lint-staged:
        specifier: ^11.2.6
        version: 11.2.6
      mini-css-extract-plugin:
        specifier: ^1.5.0
        version: 1.6.2(webpack@5.99.9)
      prettier:
        specifier: '>= 3.1.0'
        version: 3.5.3
      react-refresh:
        specifier: ^0.11.0
        version: 0.11.0
      react-refresh-typescript:
        specifier: ^2.0.3
        version: 2.0.10(react-refresh@0.11.0)(typescript@5.8.3)
      rimraf:
        specifier: ^3.0.2
        version: 3.0.2
      sass:
        specifier: ^1.32.11
        version: 1.89.0
      sass-loader:
        specifier: ^11.0.1
        version: 11.1.1(sass@1.89.0)(webpack@5.99.9)
      style-loader:
        specifier: ^2.0.0
        version: 2.0.0(webpack@5.99.9)
      ts-jest:
        specifier: ^29.1.1
        version: 29.3.4(@babel/core@7.27.1)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.1))(jest@29.7.0(@types/node@22.15.21))(typescript@5.8.3)
      ts-loader:
        specifier: ^9.0.2
        version: 9.5.2(typescript@5.8.3)(webpack@5.99.9)
      typescript:
        specifier: ^5.2.1
        version: 5.8.3
      url:
        specifier: ^0.11.0
        version: 0.11.4
      webpack:
        specifier: ^5.53.0
        version: 5.99.9(webpack-cli@4.10.0)
      webpack-bundle-analyzer:
        specifier: ^4.10.2
        version: 4.10.2
      webpack-cli:
        specifier: ^4.8.0
        version: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)
      webpack-dev-server:
        specifier: ^4.2.1
        version: 4.15.2(webpack-cli@4.10.0)(webpack@5.99.9)
      webpack-merge:
        specifier: ^5.8.0
        version: 5.10.0
      whatwg-fetch:
        specifier: 3.6.2
        version: 3.6.2

packages:

  '@adobe/css-tools@4.4.3':
    resolution: {integrity: sha1-vuu++wJk/esy0wUqyuDg2UMVqaI=}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha1-IA9xXmbVKiOyIalDVTSpHME61b4=}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.2':
    resolution: {integrity: sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.1':
    resolution: {integrity: sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.1':
    resolution: {integrity: sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.27.1':
    resolution: {integrity: sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha1-RqD276uAjVHSnOloWN0Qzocycz0=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.1':
    resolution: {integrity: sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.1':
    resolution: {integrity: sha1-/8JwEwOGB826MojmksNhHAahiqQ=}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.2':
    resolution: {integrity: sha1-V3UYvtsXos5CEq/QUuAfffCUESc=}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.27.1':
    resolution: {integrity: sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha1-ypHvRjA1MESLkGZSusLp/plB9pk=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.27.1':
    resolution: {integrity: sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime-corejs3@7.27.1':
    resolution: {integrity: sha1-OGx6c33Yk9oFb2x5clJugnAgtH0=}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@7.27.1':
    resolution: {integrity: sha1-n84xPRLJp3UH8mTedGJuh/0NxUE=}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.1':
    resolution: {integrity: sha1-TbdykCsTO73dHE96fuR3YcG58pE=}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.1':
    resolution: {integrity: sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha1-daLotRy3WKdVPWgEpZMteqznXDk=}

  '@ceridianhcm/analytics@1.33.1':
    resolution: {integrity: sha1-b1vdSHemG3usKaCAEA/75ccX1PY=}

  '@ceridianhcm/components@1.43.123':
    resolution: {integrity: sha1-oAU5N8gxw7dvN8xq/onGllp6dVs=}
    peerDependencies:
      '@platform/core': ^1.29.8
      react: 17.0.1
      react-dom: 17.0.1

  '@ceridianhcm/components@1.43.96':
    resolution: {integrity: sha1-+Y11FE8XFBLKC8f2S8X3LUoFMFQ=}
    peerDependencies:
      '@platform/core': ^1.29.8
      react: 17.0.1
      react-dom: 17.0.1

  '@ceridianhcm/eslint-config-platform@1.33.1':
    resolution: {integrity: sha1-1teVeRCcnEIpV6DEqhawDpH6+AA=}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^6.13.2
      '@typescript-eslint/parser': ^6.13.2
      eslint: ^8.54.0
      eslint-config-airbnb: ^19.0.4
      eslint-config-airbnb-typescript: ^17.1.0
      eslint-plugin-filename-rules: ^1.3.1
      eslint-plugin-html: ^7.1.0
      eslint-plugin-import: ^2.29.0
      eslint-plugin-jsx-a11y: ^6.8.0
      eslint-plugin-react: ^7.33.2
      eslint-plugin-react-hooks: ^4.6.0
      eslint-plugin-testing-library: ^6.2.0
      prettier: ^3.1.1
      typescript: ^5.2.2

  '@ceridianhcm/everest-cdk@1.43.21':
    resolution: {integrity: sha1-eIkiQn7olz9rvofZ8l0k4KY7ocU=}
    peerDependencies:
      react: 17.0.1
      react-dom: 17.0.1

  '@ceridianhcm/everest-community@1.44.3':
    resolution: {integrity: sha1-zqCD91q5SypxBp7Ubseky/qoAX4=}
    peerDependencies:
      '@ceridianhcm/analytics': ^1.17.1
      '@ceridianhcm/react-core': ^1.23.2
      '@platform/catalog': ^1.33.3
      '@platform/component': ^1.33.3
      '@platform/core': ^1.22.3

  '@ceridianhcm/everest-dayforce@1.43.99':
    resolution: {integrity: sha1-ROjFMwjrRpYkFuYH6CdiHIfdVz4=}
    peerDependencies:
      '@ceridianhcm/analytics': ^1.29.1
      '@ceridianhcm/globalization': ^1.29.5
      '@ceridianhcm/react-core': ^1.29.1
      react: 17.0.1
      react-dom: 17.0.1

  '@ceridianhcm/feature-flag-client@1.30.12':
    resolution: {integrity: sha1-o+KMZklmf8kBzcqK9LohXvbtH7s=}

  '@ceridianhcm/globalization@1.33.1':
    resolution: {integrity: sha1-2GalYElaPlaRFDAjkt+MCZlvwhU=}

  '@ceridianhcm/microfrontend-harness@1.33.1':
    resolution: {integrity: sha1-mh8phaqukqbvt83GrErdW9Xx4bE=}
    peerDependencies:
      '@ceridianhcm/feature-flag-client': ^1.30.11
      '@ceridianhcm/globalization': ^1.20.12
      '@platform/core': ^1.20.2
      '@react-rxjs/core': ^0.8.4
      '@react-rxjs/utils': ^0.9.3

  '@ceridianhcm/platform-df-assets@1.33.7':
    resolution: {integrity: sha1-J9ne7dg+6C26hPaYtLSUGNTAv6g=}

  '@ceridianhcm/react-core@1.33.1':
    resolution: {integrity: sha1-Yilegpfdc6dW30z67HkZ7Z9Vl94=}
    peerDependencies:
      '@ceridianhcm/analytics': ^1.10.24
      '@ceridianhcm/globalization': ^1.31.2
      '@platform/core': ^1.22.4
      '@platform/theme': ^1.14.11
      react: 17.0.1
      react-dom: 17.0.1
    peerDependenciesMeta:
      '@ceridianhcm/analytics':
        optional: true

  '@ceridianhcm/theme@1.40.4':
    resolution: {integrity: sha1-q+17bMuTvv9xDWblauK+z8PXtQA=}

  '@ceridianhcm/theme@1.43.1':
    resolution: {integrity: sha1-SzT3R4yDp1k5/9ZTlDRnF3/b1J8=}

  '@discoveryjs/json-ext@0.5.7':
    resolution: {integrity: sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=}
    engines: {node: '>=10.0.0'}

  '@dnd-kit/accessibility@3.1.1':
    resolution: {integrity: sha1-O0ICvWuzcKBzD2c0hneFkZvqxq8=}
    peerDependencies:
      react: '>=16.8.0'

  '@dnd-kit/core@6.0.3':
    resolution: {integrity: sha1-+ZzjEuLXWhJFqZcJv84NhENL68w=}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@dnd-kit/modifiers@6.0.0':
    resolution: {integrity: sha1-YdiDQTL3kaaOnpO+VCa+y81FwHg=}
    peerDependencies:
      '@dnd-kit/core': ^6.0.0

  '@dnd-kit/sortable@7.0.0':
    resolution: {integrity: sha1-sJSyiKi9N8t0rw7TR7XKTP5s55U=}
    peerDependencies:
      '@dnd-kit/core': ^6.0.0
      react: '>=16.8.0'

  '@dnd-kit/utilities@3.2.2':
    resolution: {integrity: sha1-WjK2rzVtxfdNYbN9b3EppAQM7Xs=}
    peerDependencies:
      react: '>=16.8.0'

  '@emotion/is-prop-valid@0.8.8':
    resolution: {integrity: sha1-2yixxDaKJZtgqXMR1qlS1P0BrBo=}

  '@emotion/memoize@0.7.4':
    resolution: {integrity: sha1-Gb8PWvGRSREcQNmLsM+CEZ9dnus=}

  '@emotion/stylis@0.8.5':
    resolution: {integrity: sha1-3qyzib1u530ef8rMzp4WxcfnjgQ=}

  '@emotion/unitless@0.7.5':
    resolution: {integrity: sha1-dyESkcGQCnALinjPr9oxYNdpSe0=}

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha1-YHCEYwxsAzmSoILebm+8GotSF1o=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha1-OIomnw8lwbatwxe1osVXFIlMcK0=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.1':
    resolution: {integrity: sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@humanwhocodes/config-array@0.13.0':
    resolution: {integrity: sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=}
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha1-Siho111taWPkI7z5C3/RvjQ0CdM=}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=}
    engines: {node: '>=8'}

  '@jest/console@29.7.0':
    resolution: {integrity: sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/core@29.7.0':
    resolution: {integrity: sha1-tszMI58w/zZglljFpeIpF1fORI8=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/environment@29.7.0':
    resolution: {integrity: sha1-JNYfVP8feG881Ac7S5RBY4O68qc=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect-utils@29.7.0':
    resolution: {integrity: sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect@29.7.0':
    resolution: {integrity: sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/fake-timers@29.7.0':
    resolution: {integrity: sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/globals@29.7.0':
    resolution: {integrity: sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/reporters@29.7.0':
    resolution: {integrity: sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/source-map@29.6.3':
    resolution: {integrity: sha1-2Quncglc83o0peuUE/G1YqCFVMQ=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-result@29.7.0':
    resolution: {integrity: sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-sequencer@29.7.0':
    resolution: {integrity: sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/transform@29.7.0':
    resolution: {integrity: sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@26.6.2':
    resolution: {integrity: sha1-vvWlMgMOHYii9abZM/hOlyJu1I4=}
    engines: {node: '>= 10.14.2'}

  '@jest/types@29.6.3':
    resolution: {integrity: sha1-ETH4z2NOfoTF53urEvBSr1hfulk=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=}

  '@jsonforms/core@3.5.1':
    resolution: {integrity: sha1-HIqpMju4Bne71TgCL02OA5UW/vA=}

  '@leichtgewicht/ip-codec@2.0.5':
    resolution: {integrity: sha1-T8VsFcWAua233DwzOhNOVAtEv7E=}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha1-PSbc443mWQ73nEfsLFV5PAatT2c=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=}
    engines: {node: '>= 10.0.0'}

  '@platform/catalog@1.33.10':
    resolution: {integrity: sha1-kIHWrYNHdG6G9LUDb9lrE9so65Y=}
    peerDependencies:
      '@ceridianhcm/globalization': ^1.31.3
      '@platform/cdk': ^1.20.2
      '@platform/core': ^1.18.2
      '@platform/theme': ^1.27.6
      react: 17.0.1
      react-dom: 17.0.1

  '@platform/cdk@1.33.1':
    resolution: {integrity: sha1-BaZSR84k4ONIzm9oFSxuvR/7iCg=}
    peerDependencies:
      react: 17.0.1
      react-dom: 17.0.1

  '@platform/component@1.33.10':
    resolution: {integrity: sha1-tjffXfqjKf+jFpxAv7ruDVtMSXg=}
    peerDependencies:
      '@ceridianhcm/globalization': ^1.31.3
      '@platform/cdk': ^1.20.2
      '@platform/core': ^1.18.2
      '@platform/theme': ^1.27.6
      react: 17.0.1
      react-dom: 17.0.1

  '@platform/core@1.33.1':
    resolution: {integrity: sha1-v2jN9no/S1pjqAm+MegWQ7N/u0c=}

  '@platform/dayforce@1.33.10':
    resolution: {integrity: sha1-8U39UfCWMg32tdj1kE/8drgdW8M=}
    peerDependencies:
      '@ceridianhcm/globalization': ^1.31.3
      '@platform/cdk': ^1.20.2
      '@platform/core': ^1.18.2
      '@platform/theme': ^1.27.6
      react: 17.0.1
      react-dom: 17.0.1

  '@platform/theme@1.33.1':
    resolution: {integrity: sha1-S7gAOjDEtYE1MvT7eHV94oVvUbE=}

  '@pmmmwh/react-refresh-webpack-plugin@0.5.16':
    resolution: {integrity: sha1-NnlbPVqWcDKnaZd3gPLcwB9OnEo=}
    engines: {node: '>= 10.13'}
    peerDependencies:
      '@types/webpack': 4.x || 5.x
      react-refresh: '>=0.10.0 <1.0.0'
      sockjs-client: ^1.4.0
      type-fest: '>=0.17.0 <5.0.0'
      webpack: '>=4.43.0 <6.0.0'
      webpack-dev-server: 3.x || 4.x || 5.x
      webpack-hot-middleware: 2.x
      webpack-plugin-serve: 0.x || 1.x
    peerDependenciesMeta:
      '@types/webpack':
        optional: true
      sockjs-client:
        optional: true
      type-fest:
        optional: true
      webpack-dev-server:
        optional: true
      webpack-hot-middleware:
        optional: true
      webpack-plugin-serve:
        optional: true

  '@polka/url@1.0.0-next.29':
    resolution: {integrity: sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=}

  '@progress/kendo-charts@1.32.1':
    resolution: {integrity: sha1-UlarEjACnHviXEt4Y9MmenLNd6c=}
    peerDependencies:
      '@progress/kendo-drawing': ^1.0.0

  '@progress/kendo-data-query@1.5.3':
    resolution: {integrity: sha1-ebmRh0oT0hQLh+31uuDTdaqL+aU=}

  '@progress/kendo-date-math@1.5.1':
    resolution: {integrity: sha1-bj2uVbCJ49SwF7ZfUCjwNKyrfMo=}

  '@progress/kendo-drawing@1.9.3':
    resolution: {integrity: sha1-bb9F1sFX0DlZpXBK2LxVmO2Fwtk=}

  '@progress/kendo-file-saver@1.1.2':
    resolution: {integrity: sha1-QJjNPU7pKmV03C+kxg2BOtNk+Q4=}

  '@progress/kendo-licensing@1.3.5':
    resolution: {integrity: sha1-WY05BoyH5k+sOTBBT02fSRtVUJk=}
    hasBin: true

  '@progress/kendo-popup-common@1.9.2':
    resolution: {integrity: sha1-1bVtIryMbOgS3SlzrSsiq/rIcg8=}

  '@progress/kendo-react-animation@3.18.0':
    resolution: {integrity: sha1-dji11cR+s0HWyk6Bp7ch+AMV71k=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-buttons@3.18.0':
    resolution: {integrity: sha1-MAuKzJoCYq/opbjinX91elqJ7JM=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-charts@3.18.0':
    resolution: {integrity: sha1-P8K8ipGhknqOukOX6Ancr129AHI=}
    peerDependencies:
      '@progress/kendo-drawing': ^1.2.0
      '@progress/kendo-react-intl': ^3.13.0
      hammerjs: ^2.0.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-common@3.18.0':
    resolution: {integrity: sha1-QOpvKbd5EOKj+ktfjq6+mylcdKg=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-data-tools@3.18.0':
    resolution: {integrity: sha1-lFFEyUr9QppXw72T6EN2tydK46c=}
    peerDependencies:
      '@progress/kendo-data-query': ^1.0.0
      '@progress/kendo-drawing': ^1.2.0
      '@progress/kendo-react-animation': ^3.13.0
      '@progress/kendo-react-buttons': ^3.13.0
      '@progress/kendo-react-dateinputs': ^3.13.0
      '@progress/kendo-react-dropdowns': ^3.13.0
      '@progress/kendo-react-inputs': ^3.13.0
      '@progress/kendo-react-intl': ^3.13.0
      '@progress/kendo-react-popup': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-dateinputs@3.18.0':
    resolution: {integrity: sha1-io37ixOWHIjBuhQRS96w3SUAc6M=}
    peerDependencies:
      '@progress/kendo-react-intl': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-dialogs@3.18.0':
    resolution: {integrity: sha1-xccsI+ddaouV8jCEPYLnM0Q+dLQ=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-dropdowns@3.18.0':
    resolution: {integrity: sha1-WfUpPVnkjzd19UrYqJSvoY+bqkg=}
    peerDependencies:
      '@progress/kendo-react-intl': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-gauges@3.18.0':
    resolution: {integrity: sha1-TxeOtrsk8VdlMO/J1rAwNXHJHkU=}
    peerDependencies:
      '@progress/kendo-drawing': ^1.2.0
      '@progress/kendo-react-intl': ^3.13.0
      hammerjs: ^2.0.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-grid@3.18.0':
    resolution: {integrity: sha1-4Me895D9+P6Sjz+D727v9wOUk/E=}
    peerDependencies:
      '@progress/kendo-data-query': ^1.0.0
      '@progress/kendo-drawing': ^1.2.0
      '@progress/kendo-react-animation': ^3.13.0
      '@progress/kendo-react-data-tools': ^3.13.0
      '@progress/kendo-react-dateinputs': ^3.13.0
      '@progress/kendo-react-dropdowns': ^3.13.0
      '@progress/kendo-react-inputs': ^3.13.0
      '@progress/kendo-react-intl': ^3.13.0
      '@progress/kendo-react-popup': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-inputs@3.18.0':
    resolution: {integrity: sha1-q4541KcdbfWa/Rg5JAI1EYXG05E=}
    peerDependencies:
      '@progress/kendo-drawing': ^1.5.12
      '@progress/kendo-react-animation': ^3.13.0
      '@progress/kendo-react-intl': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-intl@3.18.0':
    resolution: {integrity: sha1-DgJZM4d0JtH7hIq3VcfvtOZvYNY=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-labels@3.18.0':
    resolution: {integrity: sha1-k/rKbXX+4DwWobdmF/T8hQ3ABtE=}
    peerDependencies:
      '@progress/kendo-react-intl': ^3.0.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-layout@3.18.0':
    resolution: {integrity: sha1-KWVCmLoNl+UIL6Vy47JARXusiqA=}
    peerDependencies:
      '@progress/kendo-react-intl': ^3.13.0
      '@progress/kendo-react-progressbars': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-pdf@3.18.0':
    resolution: {integrity: sha1-sTflEgK1RhOBKxNbqZzXwVCEGbM=}
    peerDependencies:
      '@progress/kendo-drawing': ^1.2.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-popup@3.18.0':
    resolution: {integrity: sha1-tDi6nDPcKRT40KHlMXFQcf7FfA4=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-progressbars@3.18.0':
    resolution: {integrity: sha1-0Ufn/5A9AIFLYWOA95VkwV/V9qM=}
    peerDependencies:
      '@progress/kendo-react-animation': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-sortable@3.18.0':
    resolution: {integrity: sha1-6f7qJTf7R6I44hyl0MAkrz6xw4U=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-tooltip@3.18.0':
    resolution: {integrity: sha1-aa4j4uRBCaZa3SteVxPXud9mJWQ=}
    peerDependencies:
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-treelist@3.18.0':
    resolution: {integrity: sha1-K7vnpb938N/BFQFFVxk0F8mCXcw=}
    peerDependencies:
      '@progress/kendo-data-query': ^1.0.0
      '@progress/kendo-date-math': ^1.4.1
      '@progress/kendo-drawing': ^1.2.0
      '@progress/kendo-react-data-tools': ^3.13.0
      '@progress/kendo-react-dateinputs': ^3.13.0
      '@progress/kendo-react-dialogs': ^3.13.0
      '@progress/kendo-react-dropdowns': ^3.13.0
      '@progress/kendo-react-inputs': ^3.13.0
      '@progress/kendo-react-intl': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/kendo-react-treeview@3.18.0':
    resolution: {integrity: sha1-Yz2qdTc1TIM9yj3D9fQvXka+q6A=}
    peerDependencies:
      '@progress/kendo-react-animation': ^3.13.0
      react: ^16.8.2
      react-dom: ^16.8.2

  '@progress/pako-esm@1.0.1':
    resolution: {integrity: sha1-7rfSw/joZRmO3yP7QmRb2MtROhE=}

  '@react-dnd/asap@4.0.1':
    resolution: {integrity: sha1-UpGFCmtYzm8tolNSpk8bBnSHGqs=}

  '@react-dnd/invariant@2.0.0':
    resolution: {integrity: sha1-CdLoHNOeDnZ9faYt+TJYYPJOUX4=}

  '@react-dnd/shallowequal@2.0.0':
    resolution: {integrity: sha1-owMetUEp8sZrJ1P4QEJm7Hv2fwo=}

  '@react-rxjs/core@0.8.7':
    resolution: {integrity: sha1-RREZLy6DIIsePc215X8aZ710ffY=}
    peerDependencies:
      react: '>=16.8.0'
      rxjs: '>=6'

  '@react-rxjs/utils@0.9.7':
    resolution: {integrity: sha1-C2yUvLQ9Dx1QjysnUoL9CmT7MuE=}
    peerDependencies:
      '@react-rxjs/core': '>=0.1.0'
      react: '>=16.8.0'
      rxjs: '>=6'

  '@reduxjs/toolkit@1.9.7':
    resolution: {integrity: sha1-f8B8Cw6+xSBD+MtDUQzzRkBfeKY=}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18
      react-redux: ^7.2.1 || ^8.0.2
    peerDependenciesMeta:
      react:
        optional: true
      react-redux:
        optional: true

  '@remix-run/router@1.23.0':
    resolution: {integrity: sha1-NTkNDnd5YmwCaxE3baZ4nrg4kkI=}
    engines: {node: '>=14.0.0'}

  '@rtsao/scc@1.1.0':
    resolution: {integrity: sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=}

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=}

  '@sinonjs/commons@3.0.1':
    resolution: {integrity: sha1-ECk1fkTKkBphVYX20nc428iQhM0=}

  '@sinonjs/fake-timers@10.3.0':
    resolution: {integrity: sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=}

  '@tanstack/react-table@8.21.3':
    resolution: {integrity: sha1-LDjHR6VzHBoHF0/adkucKx+16Rs=}
    engines: {node: '>=12'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  '@tanstack/table-core@8.21.3':
    resolution: {integrity: sha1-KXdyfY/I36B5ES2fTUwBkRDxcyw=}
    engines: {node: '>=12'}

  '@telerik/kendo-draggable@2.2.1':
    resolution: {integrity: sha1-J9hv1bMnNna3RL2C286//9kRrVw=}

  '@telerik/kendo-intl@2.3.1':
    resolution: {integrity: sha1-GF/729Hj5Cz6tpvLp3mYcId9Pb0=}

  '@testing-library/dom@7.31.2':
    resolution: {integrity: sha1-3zYds49SEriFVQaKuBGfXYQajEo=}
    engines: {node: '>=10'}

  '@testing-library/jest-dom@6.6.3':
    resolution: {integrity: sha1-JrqQbPkowPgXLhgsb+IU60+fK9I=}
    engines: {node: '>=14', npm: '>=6', yarn: '>=1'}

  '@testing-library/react-hooks@8.0.1':
    resolution: {integrity: sha1-CSS71bVeDAwFAtF1RletpmlHyhI=}
    engines: {node: '>=12'}
    peerDependencies:
      '@types/react': ^16.9.0 || ^17.0.0
      react: ^16.9.0 || ^17.0.0
      react-dom: ^16.9.0 || ^17.0.0
      react-test-renderer: ^16.9.0 || ^17.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react-dom:
        optional: true
      react-test-renderer:
        optional: true

  '@testing-library/react@11.2.7':
    resolution: {integrity: sha1-sp4ulcZ2XIFXhsC8HVrtnLK/eBg=}
    engines: {node: '>=10'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  '@testing-library/user-event@14.6.1':
    resolution: {integrity: sha1-E+CaMteotwYP44MEeI6/QZfNIUk=}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha1-9UShSNOrNYAcH2M6dEH9h8LkhL8=}
    engines: {node: '>= 10'}

  '@types/aria-query@4.2.2':
    resolution: {integrity: sha1-7U4K2SMGpwT5+xMqDPz3dIbb4rw=}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=}

  '@types/body-parser@1.19.5':
    resolution: {integrity: sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=}

  '@types/bonjour@3.5.13':
    resolution: {integrity: sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=}

  '@types/buble@0.20.5':
    resolution: {integrity: sha1-LEvNqRDGxG4CcnP6jNsNNFiKIqM=}

  '@types/connect-history-api-fallback@1.5.4':
    resolution: {integrity: sha1-fecWRaEDBWtIrDzgezUguBnB1bM=}

  '@types/connect@3.4.38':
    resolution: {integrity: sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=}

  '@types/dayforce-common@1.26.10':
    resolution: {integrity: sha1-5c0Zn2jGGcNEXGPCttI9R9FNkaw=}

  '@types/dayforce-fallback@1.26.10':
    resolution: {integrity: sha1-9Gko14KbqlvE1JpeWPdbT6MTGP8=}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=}

  '@types/estree@1.0.7':
    resolution: {integrity: sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=}

  '@types/express-serve-static-core@4.19.6':
    resolution: {integrity: sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=}

  '@types/express-serve-static-core@5.0.6':
    resolution: {integrity: sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=}

  '@types/express@4.17.22':
    resolution: {integrity: sha1-FM/PEg9+tW67jKd7f6mhTSHefJY=}

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=}

  '@types/history@4.7.11':
    resolution: {integrity: sha1-VliLF66PUMU5g6Uk/DzEdDeWnWQ=}

  '@types/html-minifier-terser@6.1.0':
    resolution: {integrity: sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=}

  '@types/http-errors@2.0.4':
    resolution: {integrity: sha1-frR3JsORtzRabsNa1/TeRpz1uk8=}

  '@types/http-proxy@1.17.16':
    resolution: {integrity: sha1-3uNgcHs1s8yFr83on/7r/31/kkA=}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=}

  '@types/jest-axe@3.5.9':
    resolution: {integrity: sha1-l7Exc3GkhwfKk4JdTJkLDQdpDZk=}

  '@types/jest@29.5.14':
    resolution: {integrity: sha1-K5EJEvodaFbK3NDB+Vr33x1gSeU=}

  '@types/jsdom@20.0.1':
    resolution: {integrity: sha1-B8FLwZvS+RjBkpVBzarK6JR0SAg=}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=}

  '@types/json5@0.0.29':
    resolution: {integrity: sha1-7ihweulOEdK4J7y+UnC86n8+ce4=}

  '@types/mime@1.3.5':
    resolution: {integrity: sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=}

  '@types/node-forge@1.3.11':
    resolution: {integrity: sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=}

  '@types/node@22.15.21':
    resolution: {integrity: sha1-GW7xT+INh/fK8eezmDJ2f5qZW3c=}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=}

  '@types/prop-types@15.7.14':
    resolution: {integrity: sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=}

  '@types/qs@6.14.0':
    resolution: {integrity: sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=}

  '@types/range-parser@1.2.7':
    resolution: {integrity: sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=}

  '@types/react-dom@17.0.1':
    resolution: {integrity: sha1-2S130CC/sIPgfMjgrJ+TNZmk1Wo=}

  '@types/react-router-dom@5.3.3':
    resolution: {integrity: sha1-6da0pm/NvWUaXxBsJlajAIjMHoM=}

  '@types/react-router@5.1.20':
    resolution: {integrity: sha1-iOzKoSKoJAXvPvvKql3N2fAhOHw=}

  '@types/react-test-renderer@17.0.1':
    resolution: {integrity: sha1-MSD30cFX+6nfARja4gywKX7g4Gs=}

  '@types/react@17.0.1':
    resolution: {integrity: sha1-6x8UB96o2jvHQYecEZKqcDq5l1s=}

  '@types/retry@0.12.0':
    resolution: {integrity: sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=}

  '@types/semver@7.7.0':
    resolution: {integrity: sha1-ZMRBva4DOzeLbu99DD13wym5N44=}

  '@types/send@0.17.4':
    resolution: {integrity: sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=}

  '@types/serve-index@1.9.4':
    resolution: {integrity: sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=}

  '@types/serve-static@1.15.7':
    resolution: {integrity: sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=}

  '@types/sockjs@0.3.36':
    resolution: {integrity: sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=}

  '@types/source-list-map@0.1.6':
    resolution: {integrity: sha1-Fk4WndBheVtQuDwZ5NO+CfjTpFQ=}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=}

  '@types/tapable@1.0.12':
    resolution: {integrity: sha1-vCyrEuh5eO7on7IVdrZwNQ1thqs=}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=}

  '@types/uglify-js@3.17.5':
    resolution: {integrity: sha1-kFzgOjy78uMcvvy8aNFUl+4uF98=}

  '@types/webpack-sources@3.2.3':
    resolution: {integrity: sha1-tme9E+n6FanCZgPc5QLHmFQYw9g=}

  '@types/webpack@4.41.40':
    resolution: {integrity: sha1-QeoRz6/gjeJMPvQQxYl2NQZn4tE=}

  '@types/ws@8.18.1':
    resolution: {integrity: sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=}

  '@types/yargs@15.0.19':
    resolution: {integrity: sha1-Mo+4nkYQnsvbcMKV2W/y9G39Abk=}

  '@types/yargs@17.0.33':
    resolution: {integrity: sha1-jDIwPag+7AUKhLPHrnufki0T4y0=}

  '@typescript-eslint/eslint-plugin@6.21.0':
    resolution: {integrity: sha1-MIMMHKgf1fPCcU5STEMD4BlPnNM=}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^6.0.0 || ^6.0.0-alpha
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@6.21.0':
    resolution: {integrity: sha1-r4/PZv7uLtyGvF0c9F4zsGML81s=}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/scope-manager@6.21.0':
    resolution: {integrity: sha1-6oqb/I8VBKasXVmm3zCNOgYworE=}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@typescript-eslint/type-utils@6.21.0':
    resolution: {integrity: sha1-ZHMoHP7U2sq+gAToUhzuC9nUwB4=}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/types@6.21.0':
    resolution: {integrity: sha1-IFckxRI6j+9+zRlQdfpuhbrDQ20=}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/typescript-estree@6.21.0':
    resolution: {integrity: sha1-xHrnkB2zuL3cPs1z2v8tCJVojEY=}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@5.62.0':
    resolution: {integrity: sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/utils@6.21.0':
    resolution: {integrity: sha1-RxTnprOedzwcjpfsWH9SCEDNgTQ=}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/visitor-keys@6.21.0':
    resolution: {integrity: sha1-h6mdB3qlB+IOI4sR1WzCat5F/kc=}
    engines: {node: ^16.0.0 || >=18.0.0}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=}

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha1-qfagfysDyVyNOMRTah/ftSH/VbY=}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha1-/Moe7dscxOe27tT8eVbWgTshufs=}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha1-4KFhUiSLw42u523X4h8Vxe86sec=}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha1-lindqcRDDqtUtZEFPW3G87oFA0g=}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha1-5vce18yuRngcIGAX08FMUO+oEGs=}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=}

  '@webpack-cli/configtest@1.2.0':
    resolution: {integrity: sha1-eyDOHBJTORLDshfqaCYjZfoppvU=}
    peerDependencies:
      webpack: 4.x.x || 5.x.x
      webpack-cli: 4.x.x

  '@webpack-cli/info@1.5.0':
    resolution: {integrity: sha1-bHjBPFh0hS1uLdF/CKQfP+TCYbE=}
    peerDependencies:
      webpack-cli: 4.x.x

  '@webpack-cli/serve@1.7.0':
    resolution: {integrity: sha1-4Zk2iaxC0rFukZQ3bPtnU/YlTbE=}
    peerDependencies:
      webpack-cli: 4.x.x
      webpack-dev-server: '*'
    peerDependenciesMeta:
      webpack-dev-server:
        optional: true

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=}

  abab@2.0.6:
    resolution: {integrity: sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE=}

  accepts@1.3.8:
    resolution: {integrity: sha1-C/C+EltnAUrcsLCSHmLbe//hay4=}
    engines: {node: '>= 0.6'}

  acorn-globals@7.0.1:
    resolution: {integrity: sha1-Db8FxE+nyUMykUwCBm1b7/YsQMM=}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@8.3.4:
    resolution: {integrity: sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=}
    engines: {node: '>=0.4.0'}

  acorn@8.14.1:
    resolution: {integrity: sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@6.0.2:
    resolution: {integrity: sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=}
    engines: {node: '>= 6.0.0'}

  aggregate-error@3.1.0:
    resolution: {integrity: sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=}
    engines: {node: '>=8'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@3.5.2:
    resolution: {integrity: sha1-MfKdpatuANHC0yms97WSlhTVAU0=}
    peerDependencies:
      ajv: ^6.9.1

  ajv-keywords@5.1.0:
    resolution: {integrity: sha1-adTThaRzPNvqtElkoRcKiPh/DhY=}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=}

  ansi-colors@4.1.3:
    resolution: {integrity: sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=}
    engines: {node: '>=6'}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=}
    engines: {node: '>=8'}

  ansi-html-community@0.0.8:
    resolution: {integrity: sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-html@0.0.9:
    resolution: {integrity: sha1-ZRLQI0KuLMaBMZUmRKEpy3NM0/A=}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=}
    engines: {node: '>=10'}

  anymatch@3.1.3:
    resolution: {integrity: sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=}
    engines: {node: '>= 8'}

  argparse@1.0.10:
    resolution: {integrity: sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=}

  aria-query@4.2.2:
    resolution: {integrity: sha1-DSymyazrVriXfp/tau1+FbvS+Ds=}
    engines: {node: '>=6.0'}

  aria-query@5.3.2:
    resolution: {integrity: sha1-k/gaQ0gOM6M48ZFjo9EKUMAdzVk=}
    engines: {node: '>= 0.4'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=}
    engines: {node: '>= 0.4'}

  array-flatten@1.1.1:
    resolution: {integrity: sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=}

  array-includes@3.1.8:
    resolution: {integrity: sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=}
    engines: {node: '>= 0.4'}

  array-union@1.0.2:
    resolution: {integrity: sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=}
    engines: {node: '>=0.10.0'}

  array-union@2.1.0:
    resolution: {integrity: sha1-t5hCCtvrHego2ErNii4j0+/oXo0=}
    engines: {node: '>=8'}

  array-uniq@1.0.3:
    resolution: {integrity: sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=}
    engines: {node: '>=0.10.0'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=}
    engines: {node: '>= 0.4'}

  array.prototype.findlastindex@1.2.6:
    resolution: {integrity: sha1-z6EGXIHctk40VXybgdAS9qQhxWQ=}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=}
    engines: {node: '>= 0.4'}

  assert@2.1.0:
    resolution: {integrity: sha1-bZKiONBdwC50J8iB+4voHIRIst0=}

  ast-types-flow@0.0.8:
    resolution: {integrity: sha1-CoXhySaVdprBOkKLtlPnU4vqJ9Y=}

  astral-regex@2.0.0:
    resolution: {integrity: sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=}
    engines: {node: '>=8'}

  async-function@1.0.0:
    resolution: {integrity: sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=}
    engines: {node: '>= 0.4'}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=}
    engines: {node: '>= 0.4'}

  axe-core@3.5.6:
    resolution: {integrity: sha1-52KpDX9tvSRM6stOcnYP+KrVIbU=}
    engines: {node: '>=4'}

  axe-core@4.10.3:
    resolution: {integrity: sha1-BBRZZax4lPrdusMIYeXY8Rv9FPw=}
    engines: {node: '>=4'}

  axe-core@4.9.1:
    resolution: {integrity: sha1-/ND0SW2tCeDImbRPbEu3hI2pEq4=}
    engines: {node: '>=4'}

  axios@1.8.4:
    resolution: {integrity: sha1-eJkLtLxj0srgcpUtN0g1lQqC9Ec=}

  axobject-query@4.1.0:
    resolution: {integrity: sha1-KHaMdtDjz/IbxiqeLQtqwwBCoe4=}
    engines: {node: '>= 0.4'}

  babel-jest@29.7.0:
    resolution: {integrity: sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@29.6.3:
    resolution: {integrity: sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  babel-plugin-styled-components@2.1.4:
    resolution: {integrity: sha1-mh83x/Mu+Se0sAi1Kf60osgrEJI=}
    peerDependencies:
      styled-components: '>= 2'

  babel-preset-current-node-syntax@1.1.0:
    resolution: {integrity: sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-jest@29.6.3:
    resolution: {integrity: sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  basic-auth@1.1.0:
    resolution: {integrity: sha1-RSIe5Cn37h5QNb4/UVM/HN/SmIQ=}
    engines: {node: '>= 0.6'}

  batch@0.6.1:
    resolution: {integrity: sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=}

  big.js@5.2.2:
    resolution: {integrity: sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=}

  binary-extensions@2.3.0:
    resolution: {integrity: sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=}
    engines: {node: '>=8'}

  body-parser@1.20.3:
    resolution: {integrity: sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bonjour-service@1.3.0:
    resolution: {integrity: sha1-gNhnQwtaDaZOgqgEf8HjVb23FyI=}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=}
    engines: {node: '>=8'}

  browserslist@4.24.5:
    resolution: {integrity: sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bs-logger@0.2.6:
    resolution: {integrity: sha1-6302UwenLPl0zGzadraDVK0za9g=}
    engines: {node: '>= 6'}

  bser@2.1.1:
    resolution: {integrity: sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=}

  buble@0.19.6:
    resolution: {integrity: sha1-kVkJtr1bEe4DsciF7JFKi5dNNNM=}
    hasBin: true

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=}

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=}

  bytes@3.1.2:
    resolution: {integrity: sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=}
    engines: {node: '>= 0.8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=}
    engines: {node: '>=6'}

  camel-case@4.1.2:
    resolution: {integrity: sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=}

  camelcase@5.3.1:
    resolution: {integrity: sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=}
    engines: {node: '>=10'}

  camelize@1.0.1:
    resolution: {integrity: sha1-ibfhaIQFYzGjXWta0GQzLJHapsM=}

  caniuse-lite@1.0.30001718:
    resolution: {integrity: sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}

  chance@1.1.7:
    resolution: {integrity: sha1-6Z3eWsFmga94e1upTIJ3wJDWz+g=}

  char-regex@1.0.2:
    resolution: {integrity: sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=}
    engines: {node: '>=10'}

  chokidar@3.6.0:
    resolution: {integrity: sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.3:
    resolution: {integrity: sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=}
    engines: {node: '>= 14.16.0'}

  chromatic@11.29.0:
    resolution: {integrity: sha1-2lVtvTsEPoxqMTTRr6O7StcxdBA=}
    hasBin: true
    peerDependencies:
      '@chromatic-com/cypress': ^0.*.* || ^1.0.0
      '@chromatic-com/playwright': ^0.*.* || ^1.0.0
    peerDependenciesMeta:
      '@chromatic-com/cypress':
        optional: true
      '@chromatic-com/playwright':
        optional: true

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=}
    engines: {node: '>=6.0'}

  ci-info@3.9.0:
    resolution: {integrity: sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=}
    engines: {node: '>=8'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=}

  classnames@2.5.1:
    resolution: {integrity: sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=}

  cldrjs@0.5.5:
    resolution: {integrity: sha1-XJLKLeiaihbep2yy38TgAQRCjlI=}

  clean-css@5.3.3:
    resolution: {integrity: sha1-szBlPNO9a3UAnMJccUyue5M1HM0=}
    engines: {node: '>= 10.0'}

  clean-stack@2.2.0:
    resolution: {integrity: sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=}
    engines: {node: '>=6'}

  clean-webpack-plugin@3.0.0:
    resolution: {integrity: sha1-qZ2Ow0wcYopFQVZ6p7RXRGRgxis=}
    engines: {node: '>=8.9.0'}
    peerDependencies:
      webpack: '*'

  cli-cursor@3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=}
    engines: {node: '>=8'}

  cli-truncate@2.1.0:
    resolution: {integrity: sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=}
    engines: {node: '>=8'}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=}
    engines: {node: '>=12'}

  clone-deep@4.0.1:
    resolution: {integrity: sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=}
    engines: {node: '>=6'}

  clsx@1.2.1:
    resolution: {integrity: sha1-DdxKIKVJtZyTpBFrsm9SlMoX3BI=}
    engines: {node: '>=6'}

  clsx@2.1.1:
    resolution: {integrity: sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=}
    engines: {node: '>=6'}

  co@4.6.0:
    resolution: {integrity: sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=}

  colorette@1.4.0:
    resolution: {integrity: sha1-UZD7uHJ2JZqGrXAL/yxtb6o/ykA=}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=}

  colors@1.4.0:
    resolution: {integrity: sha1-xQSRR51MG9rtLJztMs98fcI2D3g=}
    engines: {node: '>=0.1.90'}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=}

  commander@7.2.0:
    resolution: {integrity: sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=}
    engines: {node: '>= 10'}

  commander@8.3.0:
    resolution: {integrity: sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=}
    engines: {node: '>= 12'}

  component-props@1.1.1:
    resolution: {integrity: sha1-+bffm5kntubZfJvScqqGdnDzSUQ=}

  component-xor@0.0.4:
    resolution: {integrity: sha1-xV2DzMG5TNUImk6T+niRxyY+Wao=}

  compressible@2.0.18:
    resolution: {integrity: sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=}
    engines: {node: '>= 0.6'}

  compression@1.8.0:
    resolution: {integrity: sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  confusing-browser-globals@1.0.11:
    resolution: {integrity: sha1-rkDptXzdORVAiigF69OlWFYI3IE=}

  connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=}
    engines: {node: '>=0.8'}

  content-disposition@0.5.4:
    resolution: {integrity: sha1-i4K076yCUSoCuwsdzsnSxejrW/4=}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=}

  cookie-signature@1.0.6:
    resolution: {integrity: sha1-4wOogrNCzD7oylE6eZmXNNqzriw=}

  cookie@0.7.1:
    resolution: {integrity: sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=}
    engines: {node: '>= 0.6'}

  copy-webpack-plugin@8.1.1:
    resolution: {integrity: sha1-P2l+FidkklwvDSNfOAZ2ElUI/SY=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^5.1.0

  core-js-pure@3.42.0:
    resolution: {integrity: sha1-6GxFp/O9y2CII+hy9z0a2d3wUx0=}

  core-js@3.42.0:
    resolution: {integrity: sha1-7b6R94rIz7bfjZl+dNNopoCC/jc=}

  core-util-is@1.0.3:
    resolution: {integrity: sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=}

  corser@2.0.1:
    resolution: {integrity: sha1-jtolLsqrWEDc2XXOuQ2TcMgZ/4c=}
    engines: {node: '>= 0.4.0'}

  cosmiconfig@7.1.0:
    resolution: {integrity: sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=}
    engines: {node: '>=10'}

  create-jest@29.7.0:
    resolution: {integrity: sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=}
    engines: {node: '>= 8'}

  css-color-keywords@1.0.0:
    resolution: {integrity: sha1-/qJhbcZ2spYmhrOvjb2+GAskTgU=}
    engines: {node: '>=4'}

  css-loader@5.2.7:
    resolution: {integrity: sha1-m58RHt9vsr5dxiUlZEy8nCMgZK4=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0

  css-select@4.3.0:
    resolution: {integrity: sha1-23EpsoRmYv2GKM/ElquytZ5BUps=}

  css-to-react-native@3.2.0:
    resolution: {integrity: sha1-zdgJn3ECThSeT2/hen1G7NVfHjI=}

  css-what@6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=}
    engines: {node: '>= 6'}

  css.escape@1.5.1:
    resolution: {integrity: sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=}
    engines: {node: '>=4'}
    hasBin: true

  cssfilter@0.0.10:
    resolution: {integrity: sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=}

  cssfontparser@1.2.1:
    resolution: {integrity: sha1-9AIvyPlwDGgCnVQghK+69CWj8+M=}

  cssom@0.3.8:
    resolution: {integrity: sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=}

  cssom@0.5.0:
    resolution: {integrity: sha1-0lT6ks2Lb72DgRufuu00ZjzBfDY=}

  cssstyle@2.3.0:
    resolution: {integrity: sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=}
    engines: {node: '>=8'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=}

  d3-array@3.2.4:
    resolution: {integrity: sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=}
    engines: {node: '>=12'}

  d3-axis@3.0.0:
    resolution: {integrity: sha1-xCpKE+gTHWN7dF/Clzgkz+r5MyI=}
    engines: {node: '>=12'}

  d3-brush@3.0.0:
    resolution: {integrity: sha1-b3Z8Ttjct53n7ePhwPieY+9k0xw=}
    engines: {node: '>=12'}

  d3-chord@3.0.1:
    resolution: {integrity: sha1-0VbWH0hfzoMn5qvzOctB2Mu6aWY=}
    engines: {node: '>=12'}

  d3-color@3.1.0:
    resolution: {integrity: sha1-OVsoM9+scVB/EqwvevI7+BneJOI=}
    engines: {node: '>=12'}

  d3-contour@4.0.2:
    resolution: {integrity: sha1-u5IGO8jFZjrLJCL5nHPLtsauO8w=}
    engines: {node: '>=12'}

  d3-delaunay@6.0.4:
    resolution: {integrity: sha1-mBaQOHM6ClurvtpVBU95W7nkpYs=}
    engines: {node: '>=12'}

  d3-dispatch@3.0.1:
    resolution: {integrity: sha1-X8dShOnCN1w2yDlBGgz1UMv8TV4=}
    engines: {node: '>=12'}

  d3-drag@3.0.0:
    resolution: {integrity: sha1-mUqunNI8cZ9TteEOOgphCMaWB7o=}
    engines: {node: '>=12'}

  d3-dsv@3.0.1:
    resolution: {integrity: sha1-xjr5ePTWoNCEpSpnOSK+IWB4m3M=}
    engines: {node: '>=12'}
    hasBin: true

  d3-ease@3.0.1:
    resolution: {integrity: sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=}
    engines: {node: '>=12'}

  d3-fetch@3.0.1:
    resolution: {integrity: sha1-gxQb/5hWoO21443onNz+Y9CmCiI=}
    engines: {node: '>=12'}

  d3-force@3.0.0:
    resolution: {integrity: sha1-Piuhph5wiI/j2RlOMNbRTuzhVcQ=}
    engines: {node: '>=12'}

  d3-format@3.1.0:
    resolution: {integrity: sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=}
    engines: {node: '>=12'}

  d3-geo@3.1.1:
    resolution: {integrity: sha1-YCfPUSRvmy69ZPmeAdx8M2QDOk0=}
    engines: {node: '>=12'}

  d3-hierarchy@3.1.2:
    resolution: {integrity: sha1-sBzULB7tPUbbd6WWbPcm+MCRYMY=}
    engines: {node: '>=12'}

  d3-interpolate@3.0.1:
    resolution: {integrity: sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=}
    engines: {node: '>=12'}

  d3-path@3.1.0:
    resolution: {integrity: sha1-It+TkDL7WnGuixgA1h3beFHEJSY=}
    engines: {node: '>=12'}

  d3-polygon@3.0.1:
    resolution: {integrity: sha1-C0XT3RxIopyOBX5hNWk+yAvxY5g=}
    engines: {node: '>=12'}

  d3-quadtree@3.0.1:
    resolution: {integrity: sha1-bco+i+Kzk8mp1RTau9gKkt7vGk8=}
    engines: {node: '>=12'}

  d3-random@3.0.1:
    resolution: {integrity: sha1-1JJjeNMz2cC/0eb6AZTTCuuqIPQ=}
    engines: {node: '>=12'}

  d3-scale-chromatic@3.1.0:
    resolution: {integrity: sha1-NMOdopiyPCDgLxpLI5vQ8i5/ExQ=}
    engines: {node: '>=12'}

  d3-scale@4.0.2:
    resolution: {integrity: sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=}
    engines: {node: '>=12'}

  d3-selection@3.0.0:
    resolution: {integrity: sha1-wlM4IH76csxbm9FFihpBkB8eGzE=}
    engines: {node: '>=12'}

  d3-shape@3.2.0:
    resolution: {integrity: sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=}
    engines: {node: '>=12'}

  d3-time-format@4.1.0:
    resolution: {integrity: sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=}
    engines: {node: '>=12'}

  d3-time@3.1.0:
    resolution: {integrity: sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=}
    engines: {node: '>=12'}

  d3-timer@3.0.1:
    resolution: {integrity: sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=}
    engines: {node: '>=12'}

  d3-transition@3.0.1:
    resolution: {integrity: sha1-aGn93hRIhoB3/dWYkgDLYbKhZF8=}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3

  d3-zoom@3.0.0:
    resolution: {integrity: sha1-0T9BZccyF//qpUKVzWlps+eu6PM=}
    engines: {node: '>=12'}

  d3@7.9.0:
    resolution: {integrity: sha1-V556yz10nK+IYL0XQa6NNxBwzV0=}
    engines: {node: '>=12'}

  damerau-levenshtein@1.0.8:
    resolution: {integrity: sha1-tD0obMvTa8Wy9+1ByvLQq6H4puc=}

  data-urls@3.0.2:
    resolution: {integrity: sha1-nPJKR3riK871zV9vC/vB0tO+kUM=}
    engines: {node: '>=12'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha1-BoMH+bcat2274QKROJ4CCFZgYZE=}
    engines: {node: '>= 0.4'}

  date-fns@2.30.0:
    resolution: {integrity: sha1-82fmRIOf9XiU7GrEgN5AyuSw9NA=}
    engines: {node: '>=0.11'}

  debounce@1.2.1:
    resolution: {integrity: sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution: {integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=}

  dedent@1.6.0:
    resolution: {integrity: sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}

  deepmerge@4.3.1:
    resolution: {integrity: sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=}
    engines: {node: '>=0.10.0'}

  default-gateway@6.0.3:
    resolution: {integrity: sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=}
    engines: {node: '>= 10'}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=}
    engines: {node: '>= 0.4'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=}
    engines: {node: '>=8'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=}
    engines: {node: '>= 0.4'}

  del@4.1.1:
    resolution: {integrity: sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=}
    engines: {node: '>=6'}

  delaunator@5.0.1:
    resolution: {integrity: sha1-OQMrCAU5I+kk1glP4s3hqZzFEng=}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}

  depd@1.1.2:
    resolution: {integrity: sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha1-SANzVQmti+VSk0xn32FPlOZvoBU=}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-libc@1.0.3:
    resolution: {integrity: sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=}
    engines: {node: '>=0.10'}
    hasBin: true

  detect-newline@3.1.0:
    resolution: {integrity: sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=}
    engines: {node: '>=8'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=}

  detect-node@2.1.0:
    resolution: {integrity: sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=}

  diff-sequences@29.6.3:
    resolution: {integrity: sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  dir-glob@3.0.1:
    resolution: {integrity: sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=}
    engines: {node: '>=8'}

  dnd-core@14.0.1:
    resolution: {integrity: sha1-dtAA5BxJSYMhD7IKSLg1+BogPC4=}

  dns-packet@5.6.1:
    resolution: {integrity: sha1-roiK1CWp0UeKBnQlarhm3hASzy8=}
    engines: {node: '>=6'}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=}
    engines: {node: '>=6.0.0'}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=}

  dom-accessibility-api@0.6.3:
    resolution: {integrity: sha1-mT6SXMHXPyxmLn113VpURSWaj9g=}

  dom-converter@0.2.0:
    resolution: {integrity: sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=}

  dom-helpers@3.4.0:
    resolution: {integrity: sha1-6bNpcA+Vn2Ls3lprq95LzNkWmvg=}

  dom-iterator@1.0.2:
    resolution: {integrity: sha1-ayOTScytvVlQVdpeCnOGzOnwqns=}

  dom-serializer@1.4.1:
    resolution: {integrity: sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=}

  dom-serializer@2.0.0:
    resolution: {integrity: sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=}

  domexception@4.0.0:
    resolution: {integrity: sha1-StG+VsytyG/HbQMzU5magDfQNnM=}
    engines: {node: '>=12'}

  domhandler@4.3.1:
    resolution: {integrity: sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=}
    engines: {node: '>= 4'}

  domhandler@5.0.3:
    resolution: {integrity: sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=}
    engines: {node: '>= 4'}

  domutils@2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=}

  domutils@3.2.2:
    resolution: {integrity: sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=}

  dot-case@3.0.4:
    resolution: {integrity: sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=}

  dotenv@16.5.0:
    resolution: {integrity: sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=}
    engines: {node: '>= 0.4'}

  duplexer@0.1.2:
    resolution: {integrity: sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha1-rg8PothQRe8UqBfao86azQSJ5b8=}

  ecstatic@3.3.2:
    resolution: {integrity: sha1-bR3UmBTQBZRoLGUq22YHamnUbEg=}
    hasBin: true

  ee-first@1.1.1:
    resolution: {integrity: sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=}

  ejs@3.1.10:
    resolution: {integrity: sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.157:
    resolution: {integrity: sha1-VTsSJSKse7pvGg3X1QsU8pdzb3U=}

  emittery@0.13.1:
    resolution: {integrity: sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=}
    engines: {node: '>=12'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=}

  emojis-list@3.0.0:
    resolution: {integrity: sha1-VXBmIEatKeLpFucariYKvf9Pang=}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha1-e46omAd9fkCdOsRUdOo46vCFelg=}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha1-coqwgvi3toNt5R8WN6q107lWj68=}
    engines: {node: '>=10.13.0'}

  enquirer@2.4.1:
    resolution: {integrity: sha1-kzNLP710/HCXsiSrSo+35Av0rlY=}
    engines: {node: '>=8.6'}

  entities@2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=}

  entities@4.5.0:
    resolution: {integrity: sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=}
    engines: {node: '>=0.12'}

  entities@6.0.0:
    resolution: {integrity: sha1-CcninLebCmRZqbnbnvtBisW7jlE=}
    engines: {node: '>=0.12'}

  envinfo@7.14.0:
    resolution: {integrity: sha1-JtrF21RBjypMEVkVOgsq6YCDiq4=}
    engines: {node: '>=4'}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=}

  es-abstract@1.23.10:
    resolution: {integrity: sha1-hHksFS/yiY7HPv4zwcEyOj39h/g=}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=}
    engines: {node: '>=8'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=}
    engines: {node: '>=10'}

  escodegen@2.1.0:
    resolution: {integrity: sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=}
    engines: {node: '>=6.0'}
    hasBin: true

  eslint-config-airbnb-base@15.0.0:
    resolution: {integrity: sha1-awmt2QrHnC+NcjolgOB/OSWv0jY=}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: ^7.32.0 || ^8.2.0
      eslint-plugin-import: ^2.25.2

  eslint-config-airbnb-typescript@17.1.0:
    resolution: {integrity: sha1-/alg7uSlEPCSqaHBOQNaxYiTfdw=}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^5.13.0 || ^6.0.0
      '@typescript-eslint/parser': ^5.0.0 || ^6.0.0
      eslint: ^7.32.0 || ^8.2.0
      eslint-plugin-import: ^2.25.3

  eslint-config-airbnb@19.0.4:
    resolution: {integrity: sha1-hNTDSQrXCg/6VxE4683qarCF/cM=}
    engines: {node: ^10.12.0 || ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^7.32.0 || ^8.2.0
      eslint-plugin-import: ^2.25.3
      eslint-plugin-jsx-a11y: ^6.5.1
      eslint-plugin-react: ^7.28.0
      eslint-plugin-react-hooks: ^4.3.0

  eslint-config-prettier@10.1.5:
    resolution: {integrity: sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=}

  eslint-module-utils@2.12.0:
    resolution: {integrity: sha1-/kz7lI1h9JID17CIcZgrZbmvCws=}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-filename-rules@1.3.1:
    resolution: {integrity: sha1-j7dp8sGdyDK0PBPXbBRCvKSi9KQ=}
    engines: {node: '>=6.0.0'}

  eslint-plugin-html@7.1.0:
    resolution: {integrity: sha1-rsKjdytAzPUaW+T5cvB2AFOdOz4=}

  eslint-plugin-import@2.31.0:
    resolution: {integrity: sha1-MQzn5yDKHZwLs/aa39HGvdfZ4Oc=}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jsx-a11y@6.10.2:
    resolution: {integrity: sha1-0oErsjvxq0Zl8XGOpELoNy5jhIM=}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

  eslint-plugin-react-hooks@4.6.2:
    resolution: {integrity: sha1-yCnrBsDm9ISz+7hal+V3hPMoxZY=}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-testing-library@6.5.0:
    resolution: {integrity: sha1-AlMmmCcPUlvozutHQPZu7w9vRyk=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0, npm: '>=6'}
    peerDependencies:
      eslint: ^7.5.0 || ^8.0.0 || ^9.0.0

  eslint-scope@5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.57.1:
    resolution: {integrity: sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  espree@9.6.1:
    resolution: {integrity: sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=}
    engines: {node: '>= 0.6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=}

  events@3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=}
    engines: {node: '>=10'}

  exit@0.1.2:
    resolution: {integrity: sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=}
    engines: {node: '>= 0.8.0'}

  expect@29.7.0:
    resolution: {integrity: sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  express@4.21.2:
    resolution: {integrity: sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=}
    engines: {node: '>= 0.10.0'}

  extend-shallow@2.0.1:
    resolution: {integrity: sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=}
    engines: {node: '>=0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=}

  fastest-levenshtein@1.0.16:
    resolution: {integrity: sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=}
    engines: {node: '>= 4.9.1'}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=}

  faye-websocket@0.11.4:
    resolution: {integrity: sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=}
    engines: {node: '>=0.8.0'}

  fb-watchman@2.0.2:
    resolution: {integrity: sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-loader@6.2.0:
    resolution: {integrity: sha1-uu98+OGEDfMl5DkLRISHlIDuvk0=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  filelist@1.0.4:
    resolution: {integrity: sha1-94l4oelEd1/55i50RCTyFeWDUrU=}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=}
    engines: {node: '>=8'}

  finalhandler@1.3.1:
    resolution: {integrity: sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=}
    engines: {node: '>= 0.8'}

  find-up@4.1.0:
    resolution: {integrity: sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=}
    engines: {node: ^10.12.0 || >=12.0.0}

  flat@5.0.2:
    resolution: {integrity: sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=}
    hasBin: true

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=}

  focus-lock@1.3.6:
    resolution: {integrity: sha1-lV7sHhBZHVb2eSWO25Su2xHWkc0=}
    engines: {node: '>=10'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=}
    engines: {node: '>= 0.4'}

  form-data@4.0.2:
    resolution: {integrity: sha1-Ncq73TDDznPessQtPI0+2cpReUw=}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=}
    engines: {node: '>= 0.6'}

  fresh@0.5.2:
    resolution: {integrity: sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=}
    engines: {node: '>= 0.6'}

  fs-monkey@1.0.6:
    resolution: {integrity: sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=}
    engines: {node: '>= 0.4'}

  get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha1-tf3nfyLL4185C04ImSLFC85u9mQ=}

  get-package-type@0.1.0:
    resolution: {integrity: sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=}
    engines: {node: '>=8.0.0'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=}
    engines: {node: '>= 0.4'}

  get-stream@6.0.1:
    resolution: {integrity: sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=}
    engines: {node: '>=10'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=}
    engines: {node: '>= 0.4'}

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=}

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=}

  globalize@1.7.0:
    resolution: {integrity: sha1-MhIB6xje0W0/A8jU3by10e3W1MI=}

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha1-hDKhnXjODB6DOUnDats0VAC7EXE=}
    engines: {node: '>=8'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha1-vUvpi7BC+D15b344EZkfvoKg00s=}
    engines: {node: '>=10'}

  globby@6.1.0:
    resolution: {integrity: sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=}
    engines: {node: '>=0.10.0'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=}

  gzip-size@6.0.0:
    resolution: {integrity: sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=}
    engines: {node: '>=10'}

  hammerjs@2.0.8:
    resolution: {integrity: sha1-BO93hiz/K7edMPdpIJWTAiK/YPE=}
    engines: {node: '>=0.8.0'}

  handle-thing@2.0.1:
    resolution: {integrity: sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=}

  harmony-reflect@1.6.2:
    resolution: {integrity: sha1-Mey9MuZIo00DDYattn1NR1R/5xA=}

  has-bigints@1.1.0:
    resolution: {integrity: sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=}
    engines: {node: '>= 0.4'}

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=}

  has-proto@1.2.0:
    resolution: {integrity: sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=}
    hasBin: true

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=}

  hpack.js@2.1.6:
    resolution: {integrity: sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=}

  html-encoding-sniffer@3.0.0:
    resolution: {integrity: sha1-LLGozw21JBR3blsqegTV3ZgVjek=}
    engines: {node: '>=12'}

  html-entities@2.6.0:
    resolution: {integrity: sha1-fGTx6js2gYzK49P7SLaXQgjphPg=}

  html-escaper@2.0.2:
    resolution: {integrity: sha1-39YAJ9o2o238viNiYsAKWCJoFFM=}

  html-minifier-terser@6.1.0:
    resolution: {integrity: sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=}
    engines: {node: '>=12'}
    hasBin: true

  html-webpack-plugin@5.6.3:
    resolution: {integrity: sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=}
    engines: {node: '>=10.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.20.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true

  htmlparser2@6.1.0:
    resolution: {integrity: sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=}

  htmlparser2@8.0.2:
    resolution: {integrity: sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=}

  http-deceiver@1.2.7:
    resolution: {integrity: sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=}

  http-errors@1.6.3:
    resolution: {integrity: sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha1-t3dKFIbvc892Z6ya4IWMASxXudM=}
    engines: {node: '>= 0.8'}

  http-parser-js@0.5.10:
    resolution: {integrity: sha1-syd71tftVYjiDqc79yT8vkRgkHU=}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha1-USmAAgNSDUNPFCvHj/PBcIAPK0M=}
    engines: {node: '>= 6'}

  http-proxy-middleware@2.0.9:
    resolution: {integrity: sha1-6eY9aK+qTu49FH85FJq4TAwoFe8=}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true

  http-proxy@1.18.1:
    resolution: {integrity: sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=}
    engines: {node: '>=8.0.0'}

  http-server@0.12.3:
    resolution: {integrity: sha1-ugRx0OzEJYhmFss1xPryeRQKDTc=}
    engines: {node: '>=6'}
    hasBin: true

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=}
    engines: {node: '>= 6'}

  human-signals@2.1.0:
    resolution: {integrity: sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=}
    engines: {node: '>=10.17.0'}

  husky@7.0.4:
    resolution: {integrity: sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=}
    engines: {node: '>=12'}
    hasBin: true

  iana-tz-data@2019.1.0:
    resolution: {integrity: sha1-wCrS6+x9rczGrYvsB5JhAEFga5M=}

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=}
    engines: {node: '>=0.10.0'}

  icss-utils@5.1.0:
    resolution: {integrity: sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  identity-obj-proxy@3.0.0:
    resolution: {integrity: sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=}
    engines: {node: '>=4'}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=}
    engines: {node: '>= 4'}

  immer@9.0.21:
    resolution: {integrity: sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=}

  immutable@5.1.2:
    resolution: {integrity: sha1-6BaUdkFFBeWk+mUBB7ZeEifRbUs=}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=}
    engines: {node: '>=6'}

  import-local@3.2.0:
    resolution: {integrity: sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=}

  inherits@2.0.3:
    resolution: {integrity: sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=}

  internal-slot@1.1.0:
    resolution: {integrity: sha1-HqyRdilH0vcFa8g42T4TsulgSWE=}
    engines: {node: '>= 0.4'}

  internmap@2.0.3:
    resolution: {integrity: sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=}
    engines: {node: '>=12'}

  interpret@2.2.0:
    resolution: {integrity: sha1-GnigtZZcQKVBbQB61vUK0nxBffk=}
    engines: {node: '>= 0.10'}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=}
    engines: {node: '>= 0.10'}

  ipaddr.js@2.2.0:
    resolution: {integrity: sha1-0z+nusKE9N56+UljjJ1oFXxrkug=}
    engines: {node: '>= 10'}

  is-arguments@1.2.0:
    resolution: {integrity: sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  is-async-function@2.1.1:
    resolution: {integrity: sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha1-3aejRF31ekJYPbQihoLrp8QXBnI=}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=}
    engines: {node: '>=8'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=}
    engines: {node: '>= 0.4'}

  is-docker@2.2.1:
    resolution: {integrity: sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=}
    engines: {node: '>=8'}
    hasBin: true

  is-extendable@0.1.1:
    resolution: {integrity: sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=}
    engines: {node: '>=0.10.0'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha1-7v3NxslN3QZ02chYh7+T+USpfJA=}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=}
    engines: {node: '>=8'}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha1-fRQK3DiarzARqPKipM+m+q3/sRg=}
    engines: {node: '>=6'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha1-vz7tqTEgE5T1e126KAD5GiODCco=}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha1-7elrf+HicLPERl46RlZYdkkm1i4=}
    engines: {node: '>= 0.4'}

  is-nan@1.3.2:
    resolution: {integrity: sha1-BDpUreoxdItVts1OCara+mm9nh0=}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=}
    engines: {node: '>=0.12.0'}

  is-obj@1.0.1:
    resolution: {integrity: sha1-PkcprB9f3gJc19g6iW2rn09n2w8=}
    engines: {node: '>=0.10.0'}

  is-path-cwd@2.2.0:
    resolution: {integrity: sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=}
    engines: {node: '>=6'}

  is-path-in-cwd@2.1.0:
    resolution: {integrity: sha1-v+Lcomxp85cmWkAJljYCk1oFOss=}
    engines: {node: '>=6'}

  is-path-inside@2.1.0:
    resolution: {integrity: sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=}
    engines: {node: '>=6'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=}
    engines: {node: '>=8'}

  is-plain-obj@3.0.0:
    resolution: {integrity: sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=}
    engines: {node: '>=10'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=}
    engines: {node: '>=0.10.0'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=}

  is-regex@1.2.1:
    resolution: {integrity: sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=}
    engines: {node: '>= 0.4'}

  is-regexp@1.0.0:
    resolution: {integrity: sha1-/S2INUXEa6xaYz57mgnof6LLUGk=}
    engines: {node: '>=0.10.0'}

  is-set@2.0.3:
    resolution: {integrity: sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha1-m2eES9m38ka6BwjDqT40Jpx3T28=}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha1-+sHj1TuXrVqdCunO8jifWBClwHc=}
    engines: {node: '>=8'}

  is-string@1.1.1:
    resolution: {integrity: sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=}
    engines: {node: '>= 0.4'}

  is-wsl@2.2.0:
    resolution: {integrity: sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}

  isobject@3.0.1:
    resolution: {integrity: sha1-TkMekrEalzFjaqH5yNHMvP2reN8=}
    engines: {node: '>=0.10.0'}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=}
    engines: {node: '>=8'}

  istanbul-lib-instrument@6.0.3:
    resolution: {integrity: sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=}
    engines: {node: '>=10'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha1-kIMFusmlvRdaxqdEier9D8JEWn0=}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=}
    engines: {node: '>=8'}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=}
    engines: {node: '>= 0.4'}

  jake@10.9.2:
    resolution: {integrity: sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=}
    engines: {node: '>=10'}
    hasBin: true

  jest-axe@9.0.0:
    resolution: {integrity: sha1-l5TcBCf58salIk0wrOzmGDzvsvI=}
    engines: {node: '>= 16.0.0'}

  jest-canvas-mock@2.5.2:
    resolution: {integrity: sha1-fiHr114Fq0HIkEl/a6inf5FdKtY=}

  jest-changed-files@29.7.0:
    resolution: {integrity: sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-circus@29.7.0:
    resolution: {integrity: sha1-toF6RfzINdixbVli0MAmRz7jZoo=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-cli@29.7.0:
    resolution: {integrity: sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution: {integrity: sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution: {integrity: sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-docblock@29.7.0:
    resolution: {integrity: sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-each@29.7.0:
    resolution: {integrity: sha1-FiqbPyMovdmRvqq/+7dHReVld9E=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-environment-jsdom@29.7.0:
    resolution: {integrity: sha1-0gb6NVGTPD/VGeXf21ig9ROag38=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jest-environment-node@29.7.0:
    resolution: {integrity: sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-get-type@29.6.3:
    resolution: {integrity: sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-haste-map@29.7.0:
    resolution: {integrity: sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-junit@13.2.0:
    resolution: {integrity: sha1-Zu64ZCmq+sjBdFpw9ErOGFqsuUM=}
    engines: {node: '>=10.12.0'}

  jest-leak-detector@29.7.0:
    resolution: {integrity: sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-matcher-utils@29.2.2:
    resolution: {integrity: sha1-kgL46NOlRzMmZ4TOd2PpoIaIJpw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-matcher-utils@29.7.0:
    resolution: {integrity: sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-message-util@29.7.0:
    resolution: {integrity: sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-mock@29.7.0:
    resolution: {integrity: sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha1-kwsVRhZNStWTfVVA5xHU041MrS4=}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution: {integrity: sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve-dependencies@29.7.0:
    resolution: {integrity: sha1-GwTywJXzf8d2/0CAPckpIbHohCg=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve@29.7.0:
    resolution: {integrity: sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runner@29.7.0:
    resolution: {integrity: sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runtime@29.7.0:
    resolution: {integrity: sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-snapshot@29.7.0:
    resolution: {integrity: sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-util@29.7.0:
    resolution: {integrity: sha1-I8K2K/sivoK0TemAVYAv83EPwLw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-validate@29.7.0:
    resolution: {integrity: sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-watcher@29.7.0:
    resolution: {integrity: sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-worker@27.5.1:
    resolution: {integrity: sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=}
    engines: {node: '>= 10.13.0'}

  jest-worker@29.7.0:
    resolution: {integrity: sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest@29.7.0:
    resolution: {integrity: sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=}

  js-yaml@3.14.1:
    resolution: {integrity: sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=}
    hasBin: true

  jsdom@20.0.3:
    resolution: {integrity: sha1-iGpBuh1HJvZ6iFgCjJlIn+1q1Ns=}
    engines: {node: '>=14'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@0.5.0:
    resolution: {integrity: sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}

  json5@1.0.2:
    resolution: {integrity: sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=}
    engines: {node: '>=6'}
    hasBin: true

  jsonwebtoken@9.0.2:
    resolution: {integrity: sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=}
    engines: {node: '>=12', npm: '>=6'}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha1-R2a9BajioRryIr7NGeFVdeUqhTo=}
    engines: {node: '>=4.0'}

  jwa@1.4.2:
    resolution: {integrity: sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=}

  jws@3.2.2:
    resolution: {integrity: sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=}

  kind-of@6.0.3:
    resolution: {integrity: sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=}
    engines: {node: '>=0.10.0'}

  kleur@3.0.3:
    resolution: {integrity: sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=}
    engines: {node: '>=6'}

  klona@2.0.6:
    resolution: {integrity: sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=}
    engines: {node: '>= 8'}

  language-subtag-registry@0.3.23:
    resolution: {integrity: sha1-I1KeBNnjt0Z51wFC3z/S627Fcuc=}

  language-tags@1.0.9:
    resolution: {integrity: sha1-H/3NDsD6+0sb5/ixHzBq0PnAh3c=}
    engines: {node: '>=0.10'}

  launch-editor@2.10.0:
    resolution: {integrity: sha1-XKPt/LlmffHochMQ86QPESfUvEI=}

  leven@3.1.0:
    resolution: {integrity: sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=}
    engines: {node: '>=6'}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=}
    engines: {node: '>= 0.8.0'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}

  lint-staged@11.2.6:
    resolution: {integrity: sha1-9HexrwKU2wVOWTfxcWed9juqTEM=}
    hasBin: true

  listr2@3.14.0:
    resolution: {integrity: sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4=}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true

  loader-runner@4.3.0:
    resolution: {integrity: sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=}
    engines: {node: '>=6.11.5'}

  loader-utils@2.0.4:
    resolution: {integrity: sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=}
    engines: {node: '>=8.9.0'}

  locate-path@5.0.0:
    resolution: {integrity: sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=}
    engines: {node: '>=10'}

  lodash.includes@4.3.0:
    resolution: {integrity: sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=}

  lodash.isinteger@4.0.4:
    resolution: {integrity: sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=}

  lodash.isnumber@3.0.3:
    resolution: {integrity: sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=}

  lodash.isstring@4.0.1:
    resolution: {integrity: sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=}

  lodash.memoize@4.1.2:
    resolution: {integrity: sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=}

  lodash.once@4.1.1:
    resolution: {integrity: sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  log-update@4.0.0:
    resolution: {integrity: sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=}
    engines: {node: '>=10'}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=}
    hasBin: true

  lower-case@2.0.2:
    resolution: {integrity: sha1-b6I3xj29xKgsoP2ILkci3F5jTig=}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=}

  lz-string@1.5.0:
    resolution: {integrity: sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=}
    hasBin: true

  magic-string@0.25.9:
    resolution: {integrity: sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw=}

  make-dir@4.0.0:
    resolution: {integrity: sha1-w8IwencSd82WODBfkVwprnQbYU4=}
    engines: {node: '>=10'}

  make-error@1.3.6:
    resolution: {integrity: sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=}

  makeerror@1.0.12:
    resolution: {integrity: sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=}
    engines: {node: '>= 0.4'}

  media-typer@0.3.0:
    resolution: {integrity: sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=}
    engines: {node: '>= 0.6'}

  memfs@3.6.0:
    resolution: {integrity: sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=}
    engines: {node: '>= 4.0.0'}

  memoize-one@5.2.1:
    resolution: {integrity: sha1-gzeqPEM1WBg57AHD1ZQJDOvo8A4=}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}

  methods@1.1.2:
    resolution: {integrity: sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=}
    engines: {node: '>= 0.6'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha1-zds+5PnGRTDf9kAjZmHULLajFPU=}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=}
    engines: {node: '>=6'}

  min-indent@1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=}
    engines: {node: '>=4'}

  mini-css-extract-plugin@1.6.2:
    resolution: {integrity: sha1-gxcrT9gS+PxKCdb20W+ST1OZDKg=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.4.0 || ^5.0.0

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=}

  minimatch@5.1.6:
    resolution: {integrity: sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=}
    engines: {node: '>=10'}

  minimatch@9.0.3:
    resolution: {integrity: sha1-puAMPeRMOlQr+q5wq/wiQgptqCU=}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=}

  mkdirp@1.0.4:
    resolution: {integrity: sha1-PrXtYmInVteaXw4qIh3+utdcL34=}
    engines: {node: '>=10'}
    hasBin: true

  moo-color@1.0.3:
    resolution: {integrity: sha1-1WQ1+DWcgoTYOsWAFt90J/6+znQ=}

  mrmime@2.0.1:
    resolution: {integrity: sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=}
    engines: {node: '>=10'}

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}

  multicast-dns@7.2.5:
    resolution: {integrity: sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=}
    hasBin: true

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}

  negotiator@0.6.3:
    resolution: {integrity: sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=}

  no-case@3.0.4:
    resolution: {integrity: sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=}

  node-addon-api@7.1.1:
    resolution: {integrity: sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=}

  node-forge@1.3.1:
    resolution: {integrity: sha1-vo2iryQ7JBfV9kancGY6krfp3tM=}
    engines: {node: '>= 6.13.0'}

  node-int64@0.4.0:
    resolution: {integrity: sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=}

  normalize-path@3.0.0:
    resolution: {integrity: sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=}
    engines: {node: '>=0.10.0'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha1-t+zR5e1T2o43pV4cImnguX7XSOo=}
    engines: {node: '>=8'}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=}

  nwsapi@2.2.20:
    resolution: {integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=}
    engines: {node: '>= 0.4'}

  object-is@1.1.6:
    resolution: {integrity: sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=}
    engines: {node: '>= 0.4'}

  object.groupby@1.0.3:
    resolution: {integrity: sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=}

  on-finished@2.4.1:
    resolution: {integrity: sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=}
    engines: {node: '>=6'}

  open@8.4.2:
    resolution: {integrity: sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=}
    engines: {node: '>=12'}

  opener@1.5.2:
    resolution: {integrity: sha1-XTfh81B3udysQwE3InGv3rKhNZg=}
    hasBin: true

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=}
    engines: {node: '>= 0.8.0'}

  os-homedir@1.0.2:
    resolution: {integrity: sha1-/7xJiDNuDoM94MFox+8VISGqf7M=}
    engines: {node: '>=0.10.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=}
    engines: {node: '>= 0.4'}

  p-limit@2.3.0:
    resolution: {integrity: sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=}
    engines: {node: '>=10'}

  p-locate@4.1.0:
    resolution: {integrity: sha1-o0KLtwiLOmApL2aRkni3wpetTwc=}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=}
    engines: {node: '>=10'}

  p-map@2.1.0:
    resolution: {integrity: sha1-MQko/u+cnsxltosXaTAYpmXOoXU=}
    engines: {node: '>=6'}

  p-map@4.0.0:
    resolution: {integrity: sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=}
    engines: {node: '>=10'}

  p-retry@4.6.2:
    resolution: {integrity: sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=}
    engines: {node: '>=6'}

  param-case@3.0.4:
    resolution: {integrity: sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=}
    engines: {node: '>=8'}

  parse5@7.3.0:
    resolution: {integrity: sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=}

  parseurl@1.3.3:
    resolution: {integrity: sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=}
    engines: {node: '>= 0.8'}

  pascal-case@3.1.2:
    resolution: {integrity: sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=}
    engines: {node: '>=0.10.0'}

  path-is-inside@1.0.2:
    resolution: {integrity: sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}

  path-to-regexp@0.1.12:
    resolution: {integrity: sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=}
    engines: {node: '>=8'}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha1-7RQaasBDqEnqWISY59yosVMw6Qw=}
    engines: {node: '>=0.10.0'}

  pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=}
    engines: {node: '>=6'}

  pinkie-promise@2.0.1:
    resolution: {integrity: sha1-ITXW36ejWMBprJsXh3YogihFD/o=}
    engines: {node: '>=0.10.0'}

  pinkie@2.0.4:
    resolution: {integrity: sha1-clVrgM+g1IqXToDnckjoDtT3+HA=}
    engines: {node: '>=0.10.0'}

  pirates@4.0.7:
    resolution: {integrity: sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=}
    engines: {node: '>= 6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=}
    engines: {node: '>=8'}

  please-upgrade-node@3.2.0:
    resolution: {integrity: sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=}

  portfinder@1.0.37:
    resolution: {integrity: sha1-krdU74mhGAHI7+Sw5c2EWwBkwhI=}
    engines: {node: '>= 10.12'}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=}
    engines: {node: '>= 0.4'}

  postcss-modules-extract-imports@3.1.0:
    resolution: {integrity: sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.2.0:
    resolution: {integrity: sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.2.1:
    resolution: {integrity: sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=}

  postcss@8.5.3:
    resolution: {integrity: sha1-FGO28cf7Fv4lhzbLopot41I36vs=}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=}
    engines: {node: '>= 0.8.0'}

  prettier@3.5.3:
    resolution: {integrity: sha1-T8LODWV+egLmAlSfBTsjnLff4bU=}
    engines: {node: '>=14'}
    hasBin: true

  pretty-error@4.0.0:
    resolution: {integrity: sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=}

  pretty-format@26.6.2:
    resolution: {integrity: sha1-41wnBfFMt/4v6U+geDRbREEg/JM=}
    engines: {node: '>= 10'}

  pretty-format@29.7.0:
    resolution: {integrity: sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  prism-react-renderer@1.3.5:
    resolution: {integrity: sha1-eGu2mqb3PDK6HugT++F6ARVDUIU=}
    peerDependencies:
      react: '>=0.14.9'

  process-nextick-args@2.0.1:
    resolution: {integrity: sha1-eCDZsWEgzFXKmud5JoCufbptf+I=}

  prompts@2.4.2:
    resolution: {integrity: sha1-e1fnOzpIAprRDr1E90sBcipMsGk=}
    engines: {node: '>= 6'}

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=}

  proxy-addr@2.0.7:
    resolution: {integrity: sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=}
    engines: {node: '>= 0.10'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=}

  psl@1.15.0:
    resolution: {integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=}

  punycode@1.4.1:
    resolution: {integrity: sha1-wNWmOycYgArY4esPpSachN1BhF4=}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=}
    engines: {node: '>=6'}

  pure-rand@6.1.0:
    resolution: {integrity: sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=}

  qs@6.13.0:
    resolution: {integrity: sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=}
    engines: {node: '>=0.6'}

  qs@6.14.0:
    resolution: {integrity: sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=}
    engines: {node: '>=0.6'}

  querystringify@2.2.0:
    resolution: {integrity: sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=}

  range-parser@1.2.1:
    resolution: {integrity: sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=}
    engines: {node: '>= 0.8'}

  rc-slider@11.1.8:
    resolution: {integrity: sha1-zzsw2srI+Y1E92hfcz9vfaFG/AY=}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@5.44.4:
    resolution: {integrity: sha1-ie6QN2g8ygHNYPGmu9p2FFfda6U=}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-arborist@3.4.3:
    resolution: {integrity: sha1-mCeRoH0eJ58nm+iBYskgES887pA=}
    peerDependencies:
      react: '>= 16.14'
      react-dom: '>= 16.14'

  react-clientside-effect@1.2.8:
    resolution: {integrity: sha1-C5Cp17Khgjo6EO0eo/ZR9+AwHLc=}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  react-dnd-html5-backend@14.1.0:
    resolution: {integrity: sha1-s1o6DBbdOiv7XrfsYs8MLKzoti8=}

  react-dnd@14.0.5:
    resolution: {integrity: sha1-7PJk4iCuYuNWNNm5QVAvP8oBhe0=}
    peerDependencies:
      '@types/hoist-non-react-statics': '>= 3.3.1'
      '@types/node': '>= 12'
      '@types/react': '>= 16'
      react: '>= 16.14'
    peerDependenciesMeta:
      '@types/hoist-non-react-statics':
        optional: true
      '@types/node':
        optional: true
      '@types/react':
        optional: true

  react-dom@17.0.1:
    resolution: {integrity: sha1-HeJWBHTsnw4zQoVmLt5S28VCb8Y=}
    peerDependencies:
      react: 17.0.1

  react-draggable@4.4.6:
    resolution: {integrity: sha1-YzQ+6UV3CIHKElaltvpcn1mD/h4=}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'

  react-error-boundary@3.1.4:
    resolution: {integrity: sha1-JV25KyMZcQh1eoiLAeW3KZGaveA=}
    engines: {node: '>=10', npm: '>=6'}
    peerDependencies:
      react: '>=16.13.1'

  react-focus-lock@2.13.6:
    resolution: {integrity: sha1-KXUb8uTjD2JIZzzYejR8dP8q9nI=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=}

  react-is@17.0.2:
    resolution: {integrity: sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=}

  react-is@18.3.1:
    resolution: {integrity: sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=}

  react-lifecycles-compat@3.0.4:
    resolution: {integrity: sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=}

  react-live@2.4.1:
    resolution: {integrity: sha1-ZeZ0/5ypqalfgxF6zCH/2Wisphk=}
    engines: {node: '>= 0.12.0', npm: '>= 2.0.0'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  react-redux@7.2.2:
    resolution: {integrity: sha1-A4YugDowtrnvhYLa3MgQlH90tzY=}
    peerDependencies:
      react: ^16.8.3 || ^17
      react-dom: '*'
      react-native: '*'
      redux: ^2.0.0 || ^3.0.0 || ^4.0.0-0
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  react-refresh-typescript@2.0.10:
    resolution: {integrity: sha1-jf5A0qUYZpJtl/txUNE55w1knrI=}
    peerDependencies:
      react-refresh: 0.10.x || 0.11.x || 0.12.x || 0.13.x || 0.14.x || 0.15.x || 0.16.x || 0.17.x
      typescript: ^4.8 || ^5.0

  react-refresh@0.11.0:
    resolution: {integrity: sha1-dxmLlEcz8PHxqQ55HeRUH58HQEY=}
    engines: {node: '>=0.10.0'}

  react-router-dom@6.30.1:
    resolution: {integrity: sha1-2iWAwnLdthMl5DVHhWa+lWOkojc=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.30.1:
    resolution: {integrity: sha1-7LO4g8m6bb9dMZ3byZZ0f0q59MM=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'

  react-simple-code-editor@0.11.3:
    resolution: {integrity: sha1-blrxwuUViK3tLIm4bpj6wUQhL2E=}
    peerDependencies:
      react: '*'
      react-dom: '*'

  react-style-tag@3.0.1:
    resolution: {integrity: sha1-h55FbHWB3Xkrk6zytmqON4zGPVw=}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  react-transition-group@2.9.0:
    resolution: {integrity: sha1-35zbAleWIRFRpDbGmo87l7WwfI0=}
    peerDependencies:
      react: '>=15.0.0'
      react-dom: '>=15.0.0'

  react-window@1.8.11:
    resolution: {integrity: sha1-qFe0j6hb13BC1ZzEYJZP8uBkhSU=}
    engines: {node: '>8.0.0'}
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  react@17.0.1:
    resolution: {integrity: sha1-bgYAQWvVdXTj+G2S7bo9kAhyYSc=}
    engines: {node: '>=0.10.0'}

  readable-stream@2.3.8:
    resolution: {integrity: sha1-kRJegEK7obmIf0k0X2J3Anzovps=}

  readable-stream@3.6.2:
    resolution: {integrity: sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=}
    engines: {node: '>=8.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha1-64WAFDX78qfuWPGeCSGwaPxplI0=}
    engines: {node: '>= 14.18.0'}

  rechoir@0.7.1:
    resolution: {integrity: sha1-lHipahyhNbXoj8An8D7pLWxkVoY=}
    engines: {node: '>= 0.10'}

  redent@3.0.0:
    resolution: {integrity: sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=}
    engines: {node: '>=8'}

  redux-thunk@2.4.2:
    resolution: {integrity: sha1-udBdEZlLmfepHqIj6LBM8K+l7zs=}
    peerDependencies:
      redux: ^4

  redux@4.0.5:
    resolution: {integrity: sha1-TbXeWBbheJHeioDEJCMtBvBR2T8=}

  redux@4.2.1:
    resolution: {integrity: sha1-wI9DBoJsSbXp3JAd7gRS6o/OYZc=}

  redux@5.0.1:
    resolution: {integrity: sha1-l/omiBzldGUAElWF1WQsd7bpRHs=}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=}
    engines: {node: '>= 0.4'}

  regenerate-unicode-properties@9.0.0:
    resolution: {integrity: sha1-VNCccRXh9T3CMUqXSzLBw0Tv4yY=}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=}
    engines: {node: '>= 0.4'}

  regexpu-core@4.8.0:
    resolution: {integrity: sha1-5WBbo2G2excYR4UBMnUC9EeamPA=}
    engines: {node: '>=4'}

  regjsgen@0.5.2:
    resolution: {integrity: sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=}

  regjsparser@0.7.0:
    resolution: {integrity: sha1-prZntUyIXhi1JVTLSWDvcRh+mWg=}
    hasBin: true

  relateurl@0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=}
    engines: {node: '>= 0.10'}

  renderkid@3.0.0:
    resolution: {integrity: sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=}

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=}

  reselect@4.1.8:
    resolution: {integrity: sha1-P13GceoWjczes+FBI29p8C6uxSQ=}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha1-DwB18bslRHZs9zumpuKt/ryxPy0=}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=}
    engines: {node: '>=8'}

  resolve.exports@2.0.3:
    resolution: {integrity: sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=}
    engines: {node: '>=10'}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=}
    engines: {node: '>=8'}

  retry@0.13.1:
    resolution: {integrity: sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=}
    engines: {node: '>= 4'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha1-d492xPtzHZNBTo+SX77PZMzn9so=}

  rimraf@2.7.1:
    resolution: {integrity: sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=}
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=}
    hasBin: true

  robust-predicates@3.0.2:
    resolution: {integrity: sha1-1bKFKMSCTSD8SN8ZKNQdnvoa13E=}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}

  rupture-sass@0.3.0:
    resolution: {integrity: sha1-6YHmWy82o89OvD4jZjUknJA8Dco=}

  rw@1.3.3:
    resolution: {integrity: sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=}

  rxjs@6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=}
    engines: {npm: '>=2.0.0'}

  rxjs@7.8.2:
    resolution: {integrity: sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=}
    engines: {node: '>=0.4'}

  safe-buffer@5.1.2:
    resolution: {integrity: sha1-mR7GnSluAxN0fVm9/St0XDX4go0=}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha1-f4fftnoxUHguqvGFg/9dFxGsEME=}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=}

  sass-loader@11.1.1:
    resolution: {integrity: sha1-DbRBu74Zeyr5YSW+u39L5kdrE6c=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0
      sass: ^1.3.0
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true

  sass@1.89.0:
    resolution: {integrity: sha1-bfcjYMXD7CqYM8Sa2v5XsoIGdS0=}
    engines: {node: '>=14.0.0'}
    hasBin: true

  saxes@6.0.0:
    resolution: {integrity: sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=}
    engines: {node: '>=v12.22.7'}

  scheduler@0.20.2:
    resolution: {integrity: sha1-S67jlDbjSqk7SHS93L8P6Li1DpE=}

  schema-utils@3.3.0:
    resolution: {integrity: sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=}
    engines: {node: '>= 10.13.0'}

  schema-utils@4.3.2:
    resolution: {integrity: sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=}
    engines: {node: '>= 10.13.0'}

  secure-compare@3.0.1:
    resolution: {integrity: sha1-8aAymzCLIh+uN7mXTz1XjQypmeM=}

  select-hose@2.0.0:
    resolution: {integrity: sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=}

  selfsigned@2.4.1:
    resolution: {integrity: sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=}
    engines: {node: '>=10'}

  semver-compare@1.0.0:
    resolution: {integrity: sha1-De4hahyUGrN+nvsXiPavxf9VN/w=}

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@5.0.1:
    resolution: {integrity: sha1-eIbshIBJpGJGepfT2Rjrsqr5NPQ=}

  serialize-javascript@6.0.2:
    resolution: {integrity: sha1-3voeBVyDv21Z6oBdjahiJU62psI=}

  serve-index@1.9.1:
    resolution: {integrity: sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=}
    engines: {node: '>= 0.8.0'}

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=}
    engines: {node: '>= 0.4'}

  setprototypeof@1.1.0:
    resolution: {integrity: sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=}

  setprototypeof@1.2.0:
    resolution: {integrity: sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=}

  shallow-clone@3.0.1:
    resolution: {integrity: sha1-jymBrZJTH1UDWwH7IwdppA4C76M=}
    engines: {node: '>=8'}

  shallowequal@1.1.0:
    resolution: {integrity: sha1-GI1SHelbkIdAT9TctosT3wrk5/g=}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=}
    engines: {node: '>=8'}

  shell-quote@1.8.2:
    resolution: {integrity: sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=}
    engines: {node: '>= 0.4'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=}

  sirv@2.0.4:
    resolution: {integrity: sha1-XdmnJcV4405EnzMnA+sqdORqKbA=}
    engines: {node: '>= 10'}

  sisteransi@1.0.5:
    resolution: {integrity: sha1-E01oEpd1ZDfMBcoBNw06elcQde0=}

  slash@3.0.0:
    resolution: {integrity: sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=}
    engines: {node: '>=8'}

  slice-ansi@3.0.0:
    resolution: {integrity: sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=}
    engines: {node: '>=8'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=}
    engines: {node: '>=10'}

  sockjs@0.3.24:
    resolution: {integrity: sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=}

  source-list-map@2.0.1:
    resolution: {integrity: sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.13:
    resolution: {integrity: sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=}

  source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=}
    engines: {node: '>= 8'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=}

  spdy-transport@3.0.0:
    resolution: {integrity: sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=}

  spdy@4.0.2:
    resolution: {integrity: sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=}
    engines: {node: '>=6.0.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=}

  stack-utils@2.0.6:
    resolution: {integrity: sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=}
    engines: {node: '>=10'}

  stackframe@1.3.4:
    resolution: {integrity: sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=}

  statuses@1.5.0:
    resolution: {integrity: sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=}
    engines: {node: '>= 0.8'}

  string-argv@0.3.1:
    resolution: {integrity: sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=}
    engines: {node: '>=0.6.19'}

  string-length@4.0.2:
    resolution: {integrity: sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=}
    engines: {node: '>=10'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}

  string.prototype.includes@2.0.1:
    resolution: {integrity: sha1-7O7yEoNkB2GoHb4W1scXGk7ffZI=}
    engines: {node: '>= 0.4'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=}
    engines: {node: '>= 0.4'}

  string_decoder@1.1.1:
    resolution: {integrity: sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=}

  string_decoder@1.3.0:
    resolution: {integrity: sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=}

  stringify-object@3.3.0:
    resolution: {integrity: sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=}
    engines: {node: '>=4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=}
    engines: {node: '>=4'}

  strip-bom@4.0.0:
    resolution: {integrity: sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=}
    engines: {node: '>=6'}

  strip-indent@3.0.0:
    resolution: {integrity: sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}

  style-loader@2.0.0:
    resolution: {integrity: sha1-lmlgL9RpB0DqrsE3eZoDrdu8OTw=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  styled-components@5.2.1:
    resolution: {integrity: sha1-btf60twjOCX2THGf+97dhK15EBo=}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'
      react-is: '>= 16.8.0'

  stylis@4.3.6:
    resolution: {integrity: sha1-fHuXGRy08ZXwPsq31S95Au03gyA=}

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=}
    engines: {node: '>= 0.4'}

  symbol-observable@1.2.0:
    resolution: {integrity: sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=}
    engines: {node: '>=0.10.0'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=}

  tapable@2.2.2:
    resolution: {integrity: sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=}
    engines: {node: '>=6'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.39.2:
    resolution: {integrity: sha1-WhYmAwckpnLi5bXJzZBwMIwg6Pk=}
    engines: {node: '>=10'}
    hasBin: true

  test-exclude@6.0.0:
    resolution: {integrity: sha1-BKhphmHYBepvopO2y55jrARO8V4=}
    engines: {node: '>=8'}

  text-table@0.2.0:
    resolution: {integrity: sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}

  thunky@1.1.0:
    resolution: {integrity: sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=}

  tmpl@1.0.5:
    resolution: {integrity: sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=}
    engines: {node: '>=0.6'}

  totalist@3.0.1:
    resolution: {integrity: sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=}
    engines: {node: '>=6'}

  tough-cookie@4.1.4:
    resolution: {integrity: sha1-lF8UYbRbWox2ghwz6knDrBksGzY=}
    engines: {node: '>=6'}

  tr46@3.0.0:
    resolution: {integrity: sha1-VVxOKXqVBhfo7t3vYzyH1NnWy/k=}
    engines: {node: '>=12'}

  ts-api-utils@1.4.3:
    resolution: {integrity: sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=}
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'

  ts-jest@29.3.4:
    resolution: {integrity: sha1-k1RHKs6uHThnqA6OAgFOpZAa7kE=}
    engines: {node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@babel/core': '>=7.0.0-beta.0 <8'
      '@jest/transform': ^29.0.0
      '@jest/types': ^29.0.0
      babel-jest: ^29.0.0
      esbuild: '*'
      jest: ^29.0.0
      typescript: '>=4.3 <6'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      '@jest/transform':
        optional: true
      '@jest/types':
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true

  ts-loader@9.5.2:
    resolution: {integrity: sha1-Hz1/S7cJtIeqomDo8ZswFjXQgCA=}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      typescript: '*'
      webpack: ^5.0.0

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=}

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=}

  tsutils@3.21.0:
    resolution: {integrity: sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=}
    engines: {node: '>=4'}

  type-fest@0.20.2:
    resolution: {integrity: sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=}
    engines: {node: '>=10'}

  type-fest@4.41.0:
    resolution: {integrity: sha1-auHI5XMSc8K/H1itOcuuLJGkbFg=}
    engines: {node: '>=16'}

  type-is@1.6.18:
    resolution: {integrity: sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=}
    engines: {node: '>= 0.6'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha1-hAegT314aE89JSqhoUPSt3tBYM4=}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=}
    engines: {node: '>= 0.4'}

  typescript@5.8.3:
    resolution: {integrity: sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=}
    engines: {node: '>=14.17'}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=}
    engines: {node: '>= 0.4'}

  undici-types@6.21.0:
    resolution: {integrity: sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=}

  unescape@1.0.1:
    resolution: {integrity: sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY=}
    engines: {node: '>=0.10.0'}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=}
    engines: {node: '>=4'}

  union@0.5.0:
    resolution: {integrity: sha1-ssEb6E9gU4U3uEbtuboma6AJAHU=}
    engines: {node: '>= 0.8.0'}

  universalify@0.2.0:
    resolution: {integrity: sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=}
    engines: {node: '>= 4.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}

  url-join@2.0.5:
    resolution: {integrity: sha1-WvIvGMBSoACkjXuCxenC4v7tpyg=}

  url-parse@1.5.10:
    resolution: {integrity: sha1-nTwvc2wddd070r5QfcwRHx4uqcE=}

  url@0.11.4:
    resolution: {integrity: sha1-rcp3s1YtVrcnRudrMwt/J7ZyHzw=}
    engines: {node: '>= 0.4'}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution: {integrity: sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=}

  util@0.12.5:
    resolution: {integrity: sha1-XxemBZtz22GodWaHgaHCsTa9b7w=}

  utila@0.4.0:
    resolution: {integrity: sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=}

  utils-merge@1.0.1:
    resolution: {integrity: sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=}
    engines: {node: '>= 0.4.0'}

  uuid@8.3.2:
    resolution: {integrity: sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=}
    hasBin: true

  v8-to-istanbul@9.3.0:
    resolution: {integrity: sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=}
    engines: {node: '>=10.12.0'}

  vary@1.1.2:
    resolution: {integrity: sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=}
    engines: {node: '>= 0.8'}

  vlq@1.0.1:
    resolution: {integrity: sha1-wAP258C0we3WI/1u5Qu8DWod5Gg=}

  w3c-xmlserializer@4.0.0:
    resolution: {integrity: sha1-rr3ISSDYBiIpNuPNzkCOMkiKMHM=}
    engines: {node: '>=14'}

  walker@1.0.8:
    resolution: {integrity: sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=}

  watchpack@2.4.4:
    resolution: {integrity: sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=}
    engines: {node: '>=10.13.0'}

  wbuf@1.7.3:
    resolution: {integrity: sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=}
    engines: {node: '>=12'}

  webpack-bundle-analyzer@4.10.2:
    resolution: {integrity: sha1-YzryhiwhNzC+Pb30BFbbFxtg1b0=}
    engines: {node: '>= 10.13.0'}
    hasBin: true

  webpack-cli@4.10.0:
    resolution: {integrity: sha1-N8HWnI2FIUxaZeWJN49TrsZNqzE=}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      '@webpack-cli/generators': '*'
      '@webpack-cli/migrate': '*'
      webpack: 4.x.x || 5.x.x
      webpack-bundle-analyzer: '*'
      webpack-dev-server: '*'
    peerDependenciesMeta:
      '@webpack-cli/generators':
        optional: true
      '@webpack-cli/migrate':
        optional: true
      webpack-bundle-analyzer:
        optional: true
      webpack-dev-server:
        optional: true

  webpack-dev-middleware@5.3.4:
    resolution: {integrity: sha1-63s5KBy84Q4QTrK4vytj/OSaNRc=}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  webpack-dev-server@4.15.2:
    resolution: {integrity: sha1-ngxwpCoBJWCGCtsYaYbaEkgzMXM=}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true

  webpack-merge@5.10.0:
    resolution: {integrity: sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=}
    engines: {node: '>=10.0.0'}

  webpack-sources@1.4.3:
    resolution: {integrity: sha1-7t2OwLko+/HL/plOItLYkPMwqTM=}

  webpack-sources@3.3.0:
    resolution: {integrity: sha1-jTRJ8e0/JU5yKlKaCjRKN9LRcEg=}
    engines: {node: '>=10.13.0'}

  webpack@5.99.9:
    resolution: {integrity: sha1-1955nsF9DM48g7cHRLSu21N9gkc=}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  websocket-driver@0.7.4:
    resolution: {integrity: sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=}
    engines: {node: '>=0.8.0'}

  websocket-extensions@0.1.4:
    resolution: {integrity: sha1-f4RzvIOd/YdgituV1+sHUhFXikI=}
    engines: {node: '>=0.8.0'}

  whatwg-encoding@2.0.0:
    resolution: {integrity: sha1-52NfWX/YcCCFhiaAWicp+naYrFM=}
    engines: {node: '>=12'}

  whatwg-fetch@3.6.2:
    resolution: {integrity: sha1-3O0k838mJO0CgXJdUdDi4/5nf4w=}

  whatwg-mimetype@3.0.0:
    resolution: {integrity: sha1-X6GnYjhn/xr2yj3HKta4pCCL66c=}
    engines: {node: '>=12'}

  whatwg-url@11.0.0:
    resolution: {integrity: sha1-CoSe67X68hGbkBu3b9eVwoSNQBg=}
    engines: {node: '>=12'}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha1-Yn73YkOSChB+fOjpYZHevksWwqA=}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=}
    engines: {node: '>= 8'}
    hasBin: true

  wildcard@2.0.1:
    resolution: {integrity: sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=}

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=}
    engines: {node: '>=0.10.0'}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=}

  write-file-atomic@4.0.2:
    resolution: {integrity: sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  ws@7.5.10:
    resolution: {integrity: sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.18.2:
    resolution: {integrity: sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@4.0.0:
    resolution: {integrity: sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=}
    engines: {node: '>=12'}

  xml@1.0.1:
    resolution: {integrity: sha1-eLpyAgApxbyHuKgaPPzXS0ovweU=}

  xmlchars@2.2.0:
    resolution: {integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=}

  xss@1.0.15:
    resolution: {integrity: sha1-lqDhOIbwZhBjAotBDtGxhnD05Zo=}
    engines: {node: '>= 0.10.0'}
    hasBin: true

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=}

  yaml@1.10.2:
    resolution: {integrity: sha1-IwHF/78StGfejaIzOkWeKeeSDks=}
    engines: {node: '>= 6'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=}
    engines: {node: '>=10'}

snapshots:

  '@adobe/css-tools@4.4.3': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.2': {}

  '@babel/core@7.27.1':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.1(@babel/core@7.27.1)
      '@babel/helpers': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.1':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.27.1':
    dependencies:
      '@babel/types': 7.27.1

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.2
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.24.5
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1(supports-color@5.5.0)':
    dependencies:
      '@babel/traverse': 7.27.1(supports-color@5.5.0)
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.1':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1

  '@babel/parser@7.27.2':
    dependencies:
      '@babel/types': 7.27.1

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/runtime-corejs3@7.27.1':
    dependencies:
      core-js-pure: 3.42.0

  '@babel/runtime@7.27.1': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1

  '@babel/traverse@7.27.1':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/traverse@7.27.1(supports-color@5.5.0)':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      debug: 4.4.1(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.1':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@bcoe/v8-coverage@0.2.3': {}

  '@ceridianhcm/analytics@1.33.1':
    dependencies:
      '@babel/runtime': 7.27.1

  '@ceridianhcm/components@1.43.123(@platform/core@1.33.1)(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@ceridianhcm/everest-cdk': 1.43.21(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/theme': 1.43.1
      '@dnd-kit/core': 6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@dnd-kit/modifiers': 6.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)
      '@dnd-kit/sortable': 7.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)
      '@dnd-kit/utilities': 3.2.2(react@17.0.1)
      '@platform/core': 1.33.1
      '@tanstack/react-table': 8.21.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@tanstack/table-core': 8.21.3
      classnames: 2.5.1
      date-fns: 2.30.0
      react: 17.0.1
      react-arborist: 3.4.3(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-dom: 17.0.1(react@17.0.1)
      react-focus-lock: 2.13.6(@types/react@17.0.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@types/hoist-non-react-statics'
      - '@types/node'
      - '@types/react'

  '@ceridianhcm/components@1.43.96(@platform/core@1.33.1)(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@ceridianhcm/everest-cdk': 1.43.21(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/theme': 1.43.1
      '@dnd-kit/core': 6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@dnd-kit/modifiers': 6.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)
      '@dnd-kit/sortable': 7.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)
      '@platform/core': 1.33.1
      '@tanstack/react-table': 8.21.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      classnames: 2.5.1
      date-fns: 2.30.0
      react: 17.0.1
      react-arborist: 3.4.3(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-dom: 17.0.1(react@17.0.1)
      react-focus-lock: 2.13.6(@types/react@17.0.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@types/hoist-non-react-statics'
      - '@types/node'
      - '@types/react'

  '@ceridianhcm/eslint-config-platform@1.33.1(48c13056b39a799e5d1b3b0fb4ac1e8e)':
    dependencies:
      '@typescript-eslint/eslint-plugin': 6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/parser': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      eslint: 8.57.1
      eslint-config-airbnb: 19.0.4(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1))(eslint-plugin-react-hooks@4.6.2(eslint@8.57.1))(eslint-plugin-react@7.37.5(eslint@8.57.1))(eslint@8.57.1)
      eslint-config-airbnb-typescript: 17.1.0(@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3))(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1)
      eslint-plugin-filename-rules: 1.3.1
      eslint-plugin-html: 7.1.0
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)
      eslint-plugin-jsx-a11y: 6.10.2(eslint@8.57.1)
      eslint-plugin-react: 7.37.5(eslint@8.57.1)
      eslint-plugin-react-hooks: 4.6.2(eslint@8.57.1)
      eslint-plugin-testing-library: 6.5.0(eslint@8.57.1)(typescript@5.8.3)
      prettier: 3.5.3
      typescript: 5.8.3

  '@ceridianhcm/everest-cdk@1.43.21(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@ceridianhcm/theme': 1.43.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@ceridianhcm/everest-community@1.44.3(20e46a7e59d8ce1ac6f9a67ab650e1c7)':
    dependencies:
      '@ceridianhcm/analytics': 1.33.1
      '@ceridianhcm/components': 1.43.96(@platform/core@1.33.1)(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/everest-dayforce': 1.43.99(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@ceridianhcm/react-core@1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/react-core': 1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/theme': 1.40.4
      '@platform/catalog': 1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
      '@platform/component': 1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
      '@platform/core': 1.33.1
      chromatic: 11.29.0
      clsx: 2.1.1
      jest-canvas-mock: 2.5.2
      rc-slider: 11.1.8(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-draggable: 4.4.6(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-style-tag: 3.0.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      styled-components: 5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@babel/core'
      - '@ceridianhcm/globalization'
      - '@chromatic-com/cypress'
      - '@chromatic-com/playwright'
      - '@types/hoist-non-react-statics'
      - '@types/node'
      - '@types/react'
      - react
      - react-dom
      - react-is

  '@ceridianhcm/everest-dayforce@1.43.99(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@ceridianhcm/react-core@1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@ceridianhcm/analytics': 1.33.1
      '@ceridianhcm/components': 1.43.123(@platform/core@1.33.1)(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/globalization': 1.33.1
      '@ceridianhcm/react-core': 1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@ceridianhcm/theme': 1.43.1
      '@platform/core': 1.33.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
    transitivePeerDependencies:
      - '@types/hoist-non-react-statics'
      - '@types/node'
      - '@types/react'

  '@ceridianhcm/feature-flag-client@1.30.12': {}

  '@ceridianhcm/globalization@1.33.1':
    dependencies:
      '@jsonforms/core': 3.5.1
      globalize: 1.7.0
      iana-tz-data: 2019.1.0

  '@ceridianhcm/microfrontend-harness@1.33.1(@ceridianhcm/feature-flag-client@1.30.12)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(@react-rxjs/utils@0.9.7(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(react@17.0.1)(rxjs@7.8.2))':
    dependencies:
      '@ceridianhcm/feature-flag-client': 1.30.12
      '@ceridianhcm/globalization': 1.33.1
      '@platform/core': 1.33.1
      '@react-rxjs/core': 0.8.7(react@17.0.1)(rxjs@7.8.2)
      '@react-rxjs/utils': 0.9.7(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(react@17.0.1)(rxjs@7.8.2)

  '@ceridianhcm/platform-df-assets@1.33.7': {}

  '@ceridianhcm/react-core@1.33.1(@ceridianhcm/analytics@1.33.1)(@ceridianhcm/globalization@1.33.1)(@platform/core@1.33.1)(@platform/theme@1.33.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@ceridianhcm/globalization': 1.33.1
      '@platform/core': 1.33.1
      '@platform/theme': 1.33.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      rxjs: 6.6.7
    optionalDependencies:
      '@ceridianhcm/analytics': 1.33.1

  '@ceridianhcm/theme@1.40.4': {}

  '@ceridianhcm/theme@1.43.1': {}

  '@discoveryjs/json-ext@0.5.7': {}

  '@dnd-kit/accessibility@3.1.1(react@17.0.1)':
    dependencies:
      react: 17.0.1
      tslib: 2.8.1

  '@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@dnd-kit/accessibility': 3.1.1(react@17.0.1)
      '@dnd-kit/utilities': 3.2.2(react@17.0.1)
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      tslib: 2.8.1

  '@dnd-kit/modifiers@6.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@dnd-kit/core': 6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@dnd-kit/utilities': 3.2.2(react@17.0.1)
      tslib: 2.8.1
    transitivePeerDependencies:
      - react

  '@dnd-kit/sortable@7.0.0(@dnd-kit/core@6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@dnd-kit/core': 6.0.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@dnd-kit/utilities': 3.2.2(react@17.0.1)
      react: 17.0.1
      tslib: 2.8.1

  '@dnd-kit/utilities@3.2.2(react@17.0.1)':
    dependencies:
      react: 17.0.1
      tslib: 2.8.1

  '@emotion/is-prop-valid@0.8.8':
    dependencies:
      '@emotion/memoize': 0.7.4

  '@emotion/memoize@0.7.4': {}

  '@emotion/stylis@0.8.5': {}

  '@emotion/unitless@0.7.5': {}

  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.1': {}

  '@humanwhocodes/config-array@0.13.0':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  '@jest/core@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/reporters': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@22.15.21)
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0
      jest-runner: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  '@jest/environment@29.7.0':
    dependencies:
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      jest-mock: 29.7.0

  '@jest/expect-utils@29.7.0':
    dependencies:
      jest-get-type: 29.6.3

  '@jest/expect@29.7.0':
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/fake-timers@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@sinonjs/fake-timers': 10.3.0
      '@types/node': 22.15.21
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  '@jest/globals@29.7.0':
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/types': 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/reporters@29.7.0':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      '@types/node': 22.15.21
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.3.0
    transitivePeerDependencies:
      - supports-color

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/source-map@29.6.3':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  '@jest/test-result@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/types': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@29.7.0':
    dependencies:
      '@jest/test-result': 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  '@jest/transform@29.7.0':
    dependencies:
      '@babel/core': 7.27.1
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.8
      pirates: 4.0.7
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  '@jest/types@26.6.2':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 22.15.21
      '@types/yargs': 15.0.19
      chalk: 4.1.2

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 22.15.21
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@jsonforms/core@3.5.1':
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      lodash: 4.17.21

  '@leichtgewicht/ip-codec@2.0.5': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@platform/catalog@1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)':
    dependencies:
      '@ceridianhcm/globalization': 1.33.1
      '@platform/cdk': 1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@platform/core': 1.33.1
      '@platform/theme': 1.33.1
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-date-math': 1.5.1
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-licensing': 1.3.5
      '@progress/kendo-react-buttons': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-charts': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-data-tools': 3.18.0(f67b3fa9182f091b5be5e2d922d05884)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dialogs': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-gauges': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-grid': 3.18.0(cf730bf9d1a316859d763396620f9d3a)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-layout': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-progressbars@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-pdf': 3.18.0(@progress/kendo-drawing@1.9.3)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-progressbars': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-sortable': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-tooltip': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treelist': 3.18.0(@progress/kendo-data-query@1.5.3)(@progress/kendo-date-math@1.5.1)(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-data-tools@3.18.0(f67b3fa9182f091b5be5e2d922d05884))(@progress/kendo-react-dateinputs@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dialogs@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dropdowns@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-inputs@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treeview': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@reduxjs/toolkit': 1.9.7(react-redux@7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5))(react@17.0.1)
      assert: 2.1.0
      axios: 1.8.4
      chance: 1.1.7
      colors: 1.4.0
      d3: 7.9.0
      d3-color: 3.1.0
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-live: 2.4.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-redux: 7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5)
      redux: 4.0.5
      styled-components: 5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@babel/core'
      - '@progress/kendo-react-animation'
      - '@progress/kendo-react-popup'
      - debug
      - hammerjs
      - react-is
      - react-native

  '@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@platform/component@1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)':
    dependencies:
      '@ceridianhcm/globalization': 1.33.1
      '@platform/cdk': 1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@platform/core': 1.33.1
      '@platform/theme': 1.33.1
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-date-math': 1.5.1
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-licensing': 1.3.5
      '@progress/kendo-react-buttons': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-charts': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-data-tools': 3.18.0(f67b3fa9182f091b5be5e2d922d05884)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dialogs': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-gauges': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-grid': 3.18.0(cf730bf9d1a316859d763396620f9d3a)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-layout': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-progressbars@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-pdf': 3.18.0(@progress/kendo-drawing@1.9.3)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-progressbars': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-sortable': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-tooltip': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treelist': 3.18.0(@progress/kendo-data-query@1.5.3)(@progress/kendo-date-math@1.5.1)(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-data-tools@3.18.0(f67b3fa9182f091b5be5e2d922d05884))(@progress/kendo-react-dateinputs@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dialogs@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dropdowns@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-inputs@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treeview': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@reduxjs/toolkit': 1.9.7(react-redux@7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5))(react@17.0.1)
      assert: 2.1.0
      axios: 1.8.4
      chance: 1.1.7
      colors: 1.4.0
      d3: 7.9.0
      d3-color: 3.1.0
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-live: 2.4.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-redux: 7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5)
      redux: 4.0.5
      styled-components: 5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@babel/core'
      - '@progress/kendo-react-animation'
      - '@progress/kendo-react-popup'
      - debug
      - hammerjs
      - react-is
      - react-native

  '@platform/core@1.33.1': {}

  '@platform/dayforce@1.33.10(@babel/core@7.27.1)(@ceridianhcm/globalization@1.33.1)(@platform/cdk@1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@platform/core@1.33.1)(@platform/theme@1.33.1)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)':
    dependencies:
      '@ceridianhcm/globalization': 1.33.1
      '@platform/cdk': 1.33.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@platform/core': 1.33.1
      '@platform/theme': 1.33.1
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-date-math': 1.5.1
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-licensing': 1.3.5
      '@progress/kendo-react-buttons': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-charts': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-data-tools': 3.18.0(f67b3fa9182f091b5be5e2d922d05884)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dialogs': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-gauges': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-grid': 3.18.0(cf730bf9d1a316859d763396620f9d3a)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-layout': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-progressbars@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-pdf': 3.18.0(@progress/kendo-drawing@1.9.3)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-progressbars': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-sortable': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-tooltip': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treelist': 3.18.0(@progress/kendo-data-query@1.5.3)(@progress/kendo-date-math@1.5.1)(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-data-tools@3.18.0(f67b3fa9182f091b5be5e2d922d05884))(@progress/kendo-react-dateinputs@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dialogs@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dropdowns@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-inputs@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-treeview': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@reduxjs/toolkit': 1.9.7(react-redux@7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5))(react@17.0.1)
      assert: 2.1.0
      axios: 1.8.4
      chance: 1.1.7
      colors: 1.4.0
      d3: 7.9.0
      d3-color: 3.1.0
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-live: 2.4.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react-redux: 7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5)
      redux: 4.0.5
      styled-components: 5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@babel/core'
      - '@progress/kendo-react-animation'
      - '@progress/kendo-react-popup'
      - debug
      - hammerjs
      - react-is
      - react-native

  '@platform/theme@1.33.1':
    dependencies:
      rupture-sass: 0.3.0

  '@pmmmwh/react-refresh-webpack-plugin@0.5.16(@types/webpack@4.41.40)(react-refresh@0.11.0)(type-fest@4.41.0)(webpack-dev-server@4.15.2)(webpack@5.99.9)':
    dependencies:
      ansi-html: 0.0.9
      core-js-pure: 3.42.0
      error-stack-parser: 2.1.4
      html-entities: 2.6.0
      loader-utils: 2.0.4
      react-refresh: 0.11.0
      schema-utils: 4.3.2
      source-map: 0.7.4
      webpack: 5.99.9(webpack-cli@4.10.0)
    optionalDependencies:
      '@types/webpack': 4.41.40
      type-fest: 4.41.0
      webpack-dev-server: 4.15.2(webpack-cli@4.10.0)(webpack@5.99.9)

  '@polka/url@1.0.0-next.29': {}

  '@progress/kendo-charts@1.32.1(@progress/kendo-drawing@1.9.3)':
    dependencies:
      '@progress/kendo-drawing': 1.9.3

  '@progress/kendo-data-query@1.5.3':
    dependencies:
      tslib: 1.14.1

  '@progress/kendo-date-math@1.5.1':
    dependencies:
      tslib: 1.14.1

  '@progress/kendo-drawing@1.9.3':
    dependencies:
      '@progress/pako-esm': 1.0.1

  '@progress/kendo-file-saver@1.1.2': {}

  '@progress/kendo-licensing@1.3.5':
    dependencies:
      jsonwebtoken: 9.0.2

  '@progress/kendo-popup-common@1.9.2': {}

  '@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-transition-group: 2.9.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)

  '@progress/kendo-react-buttons@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-charts@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-charts': 1.32.1(@progress/kendo-drawing@1.9.3)
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      hammerjs: 2.0.8
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-common@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@telerik/kendo-draggable': 2.2.1
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-data-tools@3.18.0(f67b3fa9182f091b5be5e2d922d05884)':
    dependencies:
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-buttons': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-dateinputs@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-date-math': 1.5.1
      '@progress/kendo-react-buttons': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-labels': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-dialogs@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-dropdowns@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-labels': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-gauges@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(hammerjs@2.0.8)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-charts': 1.32.1(@progress/kendo-drawing@1.9.3)
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      hammerjs: 2.0.8
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-grid@3.18.0(cf730bf9d1a316859d763396620f9d3a)':
    dependencies:
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-data-tools': 3.18.0(f67b3fa9182f091b5be5e2d922d05884)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-inputs@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-labels': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@telerik/kendo-intl': 2.3.1
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-labels@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-layout@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-progressbars@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-popup': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-progressbars': 3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-pdf@3.18.0(@progress/kendo-drawing@1.9.3)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-file-saver': 1.1.2
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-popup@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-popup-common': 1.9.2
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-progressbars@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-sortable@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-tooltip@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  ? '@progress/kendo-react-treelist@3.18.0(@progress/kendo-data-query@1.5.3)(@progress/kendo-date-math@1.5.1)(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-data-tools@3.18.0(f67b3fa9182f091b5be5e2d922d05884))(@progress/kendo-react-dateinputs@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dialogs@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-dropdowns@3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-inputs@3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)'
  : dependencies:
      '@progress/kendo-data-query': 1.5.3
      '@progress/kendo-date-math': 1.5.1
      '@progress/kendo-drawing': 1.9.3
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-data-tools': 3.18.0(f67b3fa9182f091b5be5e2d922d05884)
      '@progress/kendo-react-dateinputs': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dialogs': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-dropdowns': 3.18.0(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-inputs': 3.18.0(@progress/kendo-drawing@1.9.3)(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(@progress/kendo-react-intl@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-intl': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/kendo-react-treeview@3.18.0(@progress/kendo-react-animation@3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1))(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@progress/kendo-react-animation': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      '@progress/kendo-react-common': 3.18.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@progress/pako-esm@1.0.1': {}

  '@react-dnd/asap@4.0.1': {}

  '@react-dnd/invariant@2.0.0': {}

  '@react-dnd/shallowequal@2.0.0': {}

  '@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2)':
    dependencies:
      react: 17.0.1
      rxjs: 7.8.2
      use-sync-external-store: 1.5.0(react@17.0.1)

  '@react-rxjs/utils@0.9.7(@react-rxjs/core@0.8.7(react@17.0.1)(rxjs@7.8.2))(react@17.0.1)(rxjs@7.8.2)':
    dependencies:
      '@react-rxjs/core': 0.8.7(react@17.0.1)(rxjs@7.8.2)
      react: 17.0.1
      rxjs: 7.8.2

  '@reduxjs/toolkit@1.9.7(react-redux@7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5))(react@17.0.1)':
    dependencies:
      immer: 9.0.21
      redux: 4.2.1
      redux-thunk: 2.4.2(redux@4.2.1)
      reselect: 4.1.8
    optionalDependencies:
      react: 17.0.1
      react-redux: 7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5)

  '@remix-run/router@1.23.0': {}

  '@rtsao/scc@1.1.0': {}

  '@sinclair/typebox@0.27.8': {}

  '@sinonjs/commons@3.0.1':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@10.3.0':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@tanstack/react-table@8.21.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@tanstack/table-core': 8.21.3
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@tanstack/table-core@8.21.3': {}

  '@telerik/kendo-draggable@2.2.1': {}

  '@telerik/kendo-intl@2.3.1': {}

  '@testing-library/dom@7.31.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/runtime': 7.27.1
      '@types/aria-query': 4.2.2
      aria-query: 4.2.2
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 26.6.2

  '@testing-library/jest-dom@6.6.3':
    dependencies:
      '@adobe/css-tools': 4.4.3
      aria-query: 5.3.2
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.6.3
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/react-hooks@8.0.1(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@babel/runtime': 7.27.1
      react: 17.0.1
      react-error-boundary: 3.1.4(react@17.0.1)
    optionalDependencies:
      '@types/react': 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@testing-library/react@11.2.7(react-dom@17.0.1(react@17.0.1))(react@17.0.1)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@testing-library/dom': 7.31.2
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  '@testing-library/user-event@14.6.1(@testing-library/dom@7.31.2)':
    dependencies:
      '@testing-library/dom': 7.31.2

  '@tootallnate/once@2.0.0': {}

  '@types/aria-query@4.2.2': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.1

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.1

  '@types/body-parser@1.19.5':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 22.15.21

  '@types/bonjour@3.5.13':
    dependencies:
      '@types/node': 22.15.21

  '@types/buble@0.20.5':
    dependencies:
      magic-string: 0.25.9

  '@types/connect-history-api-fallback@1.5.4':
    dependencies:
      '@types/express-serve-static-core': 5.0.6
      '@types/node': 22.15.21

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 22.15.21

  '@types/dayforce-common@1.26.10': {}

  '@types/dayforce-fallback@1.26.10': {}

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.7

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.7': {}

  '@types/express-serve-static-core@4.19.6':
    dependencies:
      '@types/node': 22.15.21
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express-serve-static-core@5.0.6':
    dependencies:
      '@types/node': 22.15.21
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express@4.17.22':
    dependencies:
      '@types/body-parser': 1.19.5
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.14.0
      '@types/serve-static': 1.15.7

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 22.15.21

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 22.15.21

  '@types/history@4.7.11': {}

  '@types/html-minifier-terser@6.1.0': {}

  '@types/http-errors@2.0.4': {}

  '@types/http-proxy@1.17.16':
    dependencies:
      '@types/node': 22.15.21

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest-axe@3.5.9':
    dependencies:
      '@types/jest': 29.5.14
      axe-core: 3.5.6

  '@types/jest@29.5.14':
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  '@types/jsdom@20.0.1':
    dependencies:
      '@types/node': 22.15.21
      '@types/tough-cookie': 4.0.5
      parse5: 7.3.0

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/mime@1.3.5': {}

  '@types/minimatch@5.1.2': {}

  '@types/node-forge@1.3.11':
    dependencies:
      '@types/node': 22.15.21

  '@types/node@22.15.21':
    dependencies:
      undici-types: 6.21.0

  '@types/parse-json@4.0.2': {}

  '@types/prop-types@15.7.14': {}

  '@types/qs@6.14.0': {}

  '@types/range-parser@1.2.7': {}

  '@types/react-dom@17.0.1':
    dependencies:
      '@types/react': 17.0.1

  '@types/react-router-dom@5.3.3':
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 17.0.1
      '@types/react-router': 5.1.20

  '@types/react-router@5.1.20':
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 17.0.1

  '@types/react-test-renderer@17.0.1':
    dependencies:
      '@types/react': 17.0.1

  '@types/react@17.0.1':
    dependencies:
      '@types/prop-types': 15.7.14
      csstype: 3.1.3

  '@types/retry@0.12.0': {}

  '@types/semver@7.7.0': {}

  '@types/send@0.17.4':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 22.15.21

  '@types/serve-index@1.9.4':
    dependencies:
      '@types/express': 4.17.22

  '@types/serve-static@1.15.7':
    dependencies:
      '@types/http-errors': 2.0.4
      '@types/node': 22.15.21
      '@types/send': 0.17.4

  '@types/sockjs@0.3.36':
    dependencies:
      '@types/node': 22.15.21

  '@types/source-list-map@0.1.6': {}

  '@types/stack-utils@2.0.3': {}

  '@types/tapable@1.0.12': {}

  '@types/tough-cookie@4.0.5': {}

  '@types/uglify-js@3.17.5':
    dependencies:
      source-map: 0.6.1

  '@types/webpack-sources@3.2.3':
    dependencies:
      '@types/node': 22.15.21
      '@types/source-list-map': 0.1.6
      source-map: 0.7.4

  '@types/webpack@4.41.40':
    dependencies:
      '@types/node': 22.15.21
      '@types/tapable': 1.0.12
      '@types/uglify-js': 3.17.5
      '@types/webpack-sources': 3.2.3
      anymatch: 3.1.3
      source-map: 0.6.1

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 22.15.21

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@15.0.19':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/type-utils': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/utils': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.4.1
      eslint: 8.57.1
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      semver: 7.7.2
      ts-api-utils: 1.4.3(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.4.1
      eslint: 8.57.1
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/scope-manager@6.21.0':
    dependencies:
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/visitor-keys': 6.21.0

  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.8.3)
      '@typescript-eslint/utils': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      debug: 4.4.1
      eslint: 8.57.1
      ts-api-utils: 1.4.3(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/types@6.21.0': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.4.1
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.2
      tsutils: 3.21.0(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.4.1
      globby: 11.1.0
      is-glob: 4.0.3
      minimatch: 9.0.3
      semver: 7.7.2
      ts-api-utils: 1.4.3(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.7.0
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.8.3)
      eslint: 8.57.1
      eslint-scope: 5.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.7.0
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.8.3)
      eslint: 8.57.1
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@typescript-eslint/visitor-keys@6.21.0':
    dependencies:
      '@typescript-eslint/types': 6.21.0
      eslint-visitor-keys: 3.4.3

  '@ungap/structured-clone@1.3.0': {}

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@webpack-cli/configtest@1.2.0(webpack-cli@4.10.0)(webpack@5.99.9)':
    dependencies:
      webpack: 5.99.9(webpack-cli@4.10.0)
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)

  '@webpack-cli/info@1.5.0(webpack-cli@4.10.0)':
    dependencies:
      envinfo: 7.14.0
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)

  '@webpack-cli/serve@1.7.0(webpack-cli@4.10.0)(webpack-dev-server@4.15.2)':
    dependencies:
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)
    optionalDependencies:
      webpack-dev-server: 4.15.2(webpack-cli@4.10.0)(webpack@5.99.9)

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  abab@2.0.6: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-globals@7.0.1:
    dependencies:
      acorn: 8.14.1
      acorn-walk: 8.3.4

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-colors@4.1.3: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-html-community@0.0.8: {}

  ansi-html@0.0.9: {}

  ansi-regex@5.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  aria-query@4.2.2:
    dependencies:
      '@babel/runtime': 7.27.1
      '@babel/runtime-corejs3': 7.27.1

  aria-query@5.3.2: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-flatten@1.1.1: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1

  array-union@1.0.2:
    dependencies:
      array-uniq: 1.0.3

  array-union@2.1.0: {}

  array-uniq@1.0.3: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.findlastindex@1.2.6:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  assert@2.1.0:
    dependencies:
      call-bind: 1.0.8
      is-nan: 1.3.2
      object-is: 1.1.6
      object.assign: 4.1.7
      util: 0.12.5

  ast-types-flow@0.0.8: {}

  astral-regex@2.0.0: {}

  async-function@1.0.0: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axe-core@3.5.6: {}

  axe-core@4.10.3: {}

  axe-core@4.9.1: {}

  axios@1.8.4:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  axobject-query@4.1.0: {}

  babel-jest@29.7.0(@babel/core@7.27.1):
    dependencies:
      '@babel/core': 7.27.1
      '@jest/transform': 29.7.0
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 29.6.3(@babel/core@7.27.1)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.27.1
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.7

  babel-plugin-styled-components@2.1.4(@babel/core@7.27.1)(styled-components@5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1))(supports-color@5.5.0):
    dependencies:
      '@babel/helper-annotate-as-pure': 7.27.1
      '@babel/helper-module-imports': 7.27.1(supports-color@5.5.0)
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.1)
      lodash: 4.17.21
      picomatch: 2.3.1
      styled-components: 5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1)
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.1):
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.27.1)
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.27.1)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.27.1)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.27.1)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.27.1)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.27.1)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.27.1)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.27.1)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.27.1)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.27.1)

  babel-preset-jest@29.6.3(@babel/core@7.27.1):
    dependencies:
      '@babel/core': 7.27.1
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.27.1)

  balanced-match@1.0.2: {}

  basic-auth@1.1.0: {}

  batch@0.6.1: {}

  big.js@5.2.2: {}

  binary-extensions@2.3.0: {}

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour-service@1.3.0:
    dependencies:
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.5:
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.5.157
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.5)

  bs-logger@0.2.6:
    dependencies:
      fast-json-stable-stringify: 2.1.0

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buble@0.19.6:
    dependencies:
      chalk: 2.4.2
      magic-string: 0.25.9
      minimist: 1.2.8
      os-homedir: 1.0.2
      regexpu-core: 4.8.0
      vlq: 1.0.1

  buffer-equal-constant-time@1.0.1: {}

  buffer-from@1.1.2: {}

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  camelize@1.0.1: {}

  caniuse-lite@1.0.30001718: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chance@1.1.7: {}

  char-regex@1.0.2: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chromatic@11.29.0: {}

  chrome-trace-event@1.0.4: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.3: {}

  classnames@2.5.1: {}

  cldrjs@0.5.5: {}

  clean-css@5.3.3:
    dependencies:
      source-map: 0.6.1

  clean-stack@2.2.0: {}

  clean-webpack-plugin@3.0.0(webpack@5.99.9):
    dependencies:
      '@types/webpack': 4.41.40
      del: 4.1.1
      webpack: 5.99.9(webpack-cli@4.10.0)

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-truncate@2.1.0:
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  clsx@1.2.1: {}

  clsx@2.1.1: {}

  co@4.6.0: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@1.4.0: {}

  colorette@2.0.20: {}

  colors@1.4.0: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@7.2.0: {}

  commander@8.3.0: {}

  component-props@1.1.1: {}

  component-xor@0.0.4: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.8.0:
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  confusing-browser-globals@1.0.11: {}

  connect-history-api-fallback@2.0.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  copy-webpack-plugin@8.1.1(webpack@5.99.9):
    dependencies:
      fast-glob: 3.3.3
      glob-parent: 5.1.2
      globby: 11.1.0
      normalize-path: 3.0.0
      p-limit: 3.1.0
      schema-utils: 3.3.0
      serialize-javascript: 5.0.1
      webpack: 5.99.9(webpack-cli@4.10.0)

  core-js-pure@3.42.0: {}

  core-js@3.42.0: {}

  core-util-is@1.0.3: {}

  corser@2.0.1: {}

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  create-jest@29.7.0(@types/node@22.15.21):
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@22.15.21)
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-color-keywords@1.0.0: {}

  css-loader@5.2.7(webpack@5.99.9):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      loader-utils: 2.0.4
      postcss: 8.5.3
      postcss-modules-extract-imports: 3.1.0(postcss@8.5.3)
      postcss-modules-local-by-default: 4.2.0(postcss@8.5.3)
      postcss-modules-scope: 3.2.1(postcss@8.5.3)
      postcss-modules-values: 4.0.0(postcss@8.5.3)
      postcss-value-parser: 4.2.0
      schema-utils: 3.3.0
      semver: 7.7.2
      webpack: 5.99.9(webpack-cli@4.10.0)

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  css-what@6.1.0: {}

  css.escape@1.5.1: {}

  cssesc@3.0.0: {}

  cssfilter@0.0.10: {}

  cssfontparser@1.2.1: {}

  cssom@0.3.8: {}

  cssom@0.5.0: {}

  cssstyle@2.3.0:
    dependencies:
      cssom: 0.3.8

  csstype@3.1.3: {}

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-axis@3.0.0: {}

  d3-brush@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  d3-chord@3.0.1:
    dependencies:
      d3-path: 3.1.0

  d3-color@3.1.0: {}

  d3-contour@4.0.2:
    dependencies:
      d3-array: 3.2.4

  d3-delaunay@6.0.4:
    dependencies:
      delaunator: 5.0.1

  d3-dispatch@3.0.1: {}

  d3-drag@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0

  d3-dsv@3.0.1:
    dependencies:
      commander: 7.2.0
      iconv-lite: 0.6.3
      rw: 1.3.3

  d3-ease@3.0.1: {}

  d3-fetch@3.0.1:
    dependencies:
      d3-dsv: 3.0.1

  d3-force@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-quadtree: 3.0.1
      d3-timer: 3.0.1

  d3-format@3.1.0: {}

  d3-geo@3.1.1:
    dependencies:
      d3-array: 3.2.4

  d3-hierarchy@3.1.2: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@3.1.0: {}

  d3-polygon@3.0.1: {}

  d3-quadtree@3.0.1: {}

  d3-random@3.0.1: {}

  d3-scale-chromatic@3.1.0:
    dependencies:
      d3-color: 3.1.0
      d3-interpolate: 3.0.1

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-selection@3.0.0: {}

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  d3-transition@3.0.1(d3-selection@3.0.0):
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1

  d3-zoom@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  d3@7.9.0:
    dependencies:
      d3-array: 3.2.4
      d3-axis: 3.0.0
      d3-brush: 3.0.0
      d3-chord: 3.0.1
      d3-color: 3.1.0
      d3-contour: 4.0.2
      d3-delaunay: 6.0.4
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-dsv: 3.0.1
      d3-ease: 3.0.1
      d3-fetch: 3.0.1
      d3-force: 3.0.0
      d3-format: 3.1.0
      d3-geo: 3.1.1
      d3-hierarchy: 3.1.2
      d3-interpolate: 3.0.1
      d3-path: 3.1.0
      d3-polygon: 3.0.1
      d3-quadtree: 3.0.1
      d3-random: 3.0.1
      d3-scale: 4.0.2
      d3-scale-chromatic: 3.1.0
      d3-selection: 3.0.0
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-time-format: 4.1.0
      d3-timer: 3.0.1
      d3-transition: 3.0.1(d3-selection@3.0.0)
      d3-zoom: 3.0.0

  damerau-levenshtein@1.0.8: {}

  data-urls@3.0.2:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 3.0.0
      whatwg-url: 11.0.0

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.27.1

  debounce@1.2.1: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  debug@4.4.1(supports-color@5.5.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 5.5.0

  debug@4.4.1(supports-color@8.1.1):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 8.1.1

  decimal.js@10.5.0: {}

  dedent@1.6.0: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  del@4.1.1:
    dependencies:
      '@types/glob': 7.2.0
      globby: 6.1.0
      is-path-cwd: 2.2.0
      is-path-in-cwd: 2.1.0
      p-map: 2.1.0
      pify: 4.0.1
      rimraf: 2.7.1

  delaunator@5.0.1:
    dependencies:
      robust-predicates: 3.0.2

  delayed-stream@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-libc@1.0.3:
    optional: true

  detect-newline@3.1.0: {}

  detect-node-es@1.1.0: {}

  detect-node@2.1.0: {}

  diff-sequences@29.6.3: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dnd-core@14.0.1:
    dependencies:
      '@react-dnd/asap': 4.0.1
      '@react-dnd/invariant': 2.0.0
      redux: 4.2.1

  dns-packet@5.6.1:
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.5

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-accessibility-api@0.5.16: {}

  dom-accessibility-api@0.6.3: {}

  dom-converter@0.2.0:
    dependencies:
      utila: 0.4.0

  dom-helpers@3.4.0:
    dependencies:
      '@babel/runtime': 7.27.1

  dom-iterator@1.0.2:
    dependencies:
      component-props: 1.1.1
      component-xor: 0.0.4

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domexception@4.0.0:
    dependencies:
      webidl-conversions: 7.0.0

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dotenv@16.5.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer@0.1.2: {}

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  ecstatic@3.3.2:
    dependencies:
      he: 1.2.0
      mime: 1.6.0
      minimist: 1.2.8
      url-join: 2.0.5

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.157: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  enquirer@2.4.1:
    dependencies:
      ansi-colors: 4.1.3
      strip-ansi: 6.0.1

  entities@2.2.0: {}

  entities@4.5.0: {}

  entities@6.0.0: {}

  envinfo@7.14.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.23.10:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-config-airbnb-base@15.0.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1):
    dependencies:
      confusing-browser-globals: 1.0.11
      eslint: 8.57.1
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)
      object.assign: 4.1.7
      object.entries: 1.1.9
      semver: 6.3.1

  eslint-config-airbnb-typescript@17.1.0(@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3))(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1):
    dependencies:
      '@typescript-eslint/eslint-plugin': 6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)
      '@typescript-eslint/parser': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      eslint: 8.57.1
      eslint-config-airbnb-base: 15.0.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1)
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)

  eslint-config-airbnb@19.0.4(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1))(eslint-plugin-react-hooks@4.6.2(eslint@8.57.1))(eslint-plugin-react@7.37.5(eslint@8.57.1))(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1
      eslint-config-airbnb-base: 15.0.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1))(eslint@8.57.1)
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)
      eslint-plugin-jsx-a11y: 6.10.2(eslint@8.57.1)
      eslint-plugin-react: 7.37.5(eslint@8.57.1)
      eslint-plugin-react-hooks: 4.6.2(eslint@8.57.1)
      object.assign: 4.1.7
      object.entries: 1.1.9

  eslint-config-prettier@10.1.5(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.12.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-filename-rules@1.3.1: {}

  eslint-plugin-html@7.1.0:
    dependencies:
      htmlparser2: 8.0.2

  eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.6
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1)
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 6.21.0(eslint@8.57.1)(typescript@5.8.3)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    dependencies:
      aria-query: 5.3.2
      array-includes: 3.1.8
      array.prototype.flatmap: 1.3.3
      ast-types-flow: 0.0.8
      axe-core: 4.10.3
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      eslint: 8.57.1
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.1.0
      string.prototype.includes: 2.0.1

  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    dependencies:
      eslint: 8.57.1

  eslint-plugin-react@7.37.5(eslint@8.57.1):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 8.57.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-testing-library@6.5.0(eslint@8.57.1)(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.1)(typescript@5.8.3)
      eslint: 8.57.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.1:
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@8.57.1)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.3.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 3.4.3

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expect@29.7.0:
    dependencies:
      '@jest/expect-utils': 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  express@4.21.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastest-levenshtein@1.0.16: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-loader@6.2.0(webpack@5.99.9):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.99.9(webpack-cli@4.10.0)

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2

  flat@5.0.2: {}

  flatted@3.3.3: {}

  focus-lock@1.3.6:
    dependencies:
      tslib: 2.8.1

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fresh@0.5.2: {}

  fs-monkey@1.0.6: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-own-enumerable-property-symbols@3.0.2: {}

  get-package-type@0.1.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@6.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globalize@1.7.0:
    dependencies:
      cldrjs: 0.5.5

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globby@6.1.0:
    dependencies:
      array-union: 1.0.2
      glob: 7.2.3
      object-assign: 4.1.1
      pify: 2.3.0
      pinkie-promise: 2.0.1

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  hammerjs@2.0.8: {}

  handle-thing@2.0.1: {}

  harmony-reflect@1.6.2: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  html-encoding-sniffer@3.0.0:
    dependencies:
      whatwg-encoding: 2.0.0

  html-entities@2.6.0: {}

  html-escaper@2.0.2: {}

  html-minifier-terser@6.1.0:
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.39.2

  html-webpack-plugin@5.6.3(webpack@5.99.9):
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.2
    optionalDependencies:
      webpack: 5.99.9(webpack-cli@4.10.0)

  htmlparser2@6.1.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  htmlparser2@8.0.2:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 4.5.0

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.10: {}

  http-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  http-proxy-middleware@2.0.9(@types/express@4.17.22):
    dependencies:
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    optionalDependencies:
      '@types/express': 4.17.22
    transitivePeerDependencies:
      - debug

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  http-server@0.12.3:
    dependencies:
      basic-auth: 1.1.0
      colors: 1.4.0
      corser: 2.0.1
      ecstatic: 3.3.2
      http-proxy: 1.18.1
      minimist: 1.2.8
      opener: 1.5.2
      portfinder: 1.0.37
      secure-compare: 3.0.1
      union: 0.5.0
    transitivePeerDependencies:
      - debug
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  husky@7.0.4: {}

  iana-tz-data@2019.1.0: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  icss-utils@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  identity-obj-proxy@3.0.0:
    dependencies:
      harmony-reflect: 1.6.2

  ignore@5.3.2: {}

  immer@9.0.21: {}

  immutable@5.1.2: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  internmap@2.0.3: {}

  interpret@2.2.0: {}

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.2.0: {}

  is-arguments@1.2.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-docker@2.2.1: {}

  is-extendable@0.1.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-nan@1.3.2:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@1.0.1: {}

  is-path-cwd@2.2.0: {}

  is-path-in-cwd@2.1.0:
    dependencies:
      is-path-inside: 2.1.0

  is-path-inside@2.1.0:
    dependencies:
      path-is-inside: 1.0.2

  is-path-inside@3.0.3: {}

  is-plain-obj@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-regexp@1.0.0: {}

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.27.1
      '@babel/parser': 7.27.2
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.3:
    dependencies:
      '@babel/core': 7.27.1
      '@babel/parser': 7.27.2
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-axe@9.0.0:
    dependencies:
      axe-core: 4.9.1
      chalk: 4.1.2
      jest-matcher-utils: 29.2.2
      lodash.merge: 4.6.2

  jest-canvas-mock@2.5.2:
    dependencies:
      cssfontparser: 1.2.1
      moo-color: 1.0.3

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.6.0
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@22.15.21):
    dependencies:
      '@jest/core': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@22.15.21)
      exit: 0.1.2
      import-local: 3.2.0
      jest-config: 29.7.0(@types/node@22.15.21)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@22.15.21):
    dependencies:
      '@babel/core': 7.27.1
      '@jest/test-sequencer': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.27.1)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      '@types/node': 22.15.21
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-jsdom@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/jsdom': 20.0.1
      '@types/node': 22.15.21
      jest-mock: 29.7.0
      jest-util: 29.7.0
      jsdom: 20.0.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jest-environment-node@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/graceful-fs': 4.1.9
      '@types/node': 22.15.21
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-junit@13.2.0:
    dependencies:
      mkdirp: 1.0.4
      strip-ansi: 6.0.1
      uuid: 8.3.2
      xml: 1.0.1

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.2.2:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0:
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.10
      resolve.exports: 2.0.3
      slash: 3.0.0

  jest-runner@29.7.0:
    dependencies:
      '@jest/console': 29.7.0
      '@jest/environment': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/globals': 29.7.0
      '@jest/source-map': 29.6.3
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      chalk: 4.1.2
      cjs-module-lexer: 1.4.3
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0:
    dependencies:
      '@babel/core': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.1)
      '@babel/plugin-syntax-typescript': 7.27.1(@babel/core@7.27.1)
      '@babel/types': 7.27.1
      '@jest/expect-utils': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.27.1)
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watcher@29.7.0:
    dependencies:
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.15.21
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 22.15.21
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@29.7.0:
    dependencies:
      '@types/node': 22.15.21
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@22.15.21):
    dependencies:
      '@jest/core': 29.7.0
      '@jest/types': 29.6.3
      import-local: 3.2.0
      jest-cli: 29.7.0(@types/node@22.15.21)
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@20.0.3:
    dependencies:
      abab: 2.0.6
      acorn: 8.14.1
      acorn-globals: 7.0.1
      cssom: 0.5.0
      cssstyle: 2.3.0
      data-urls: 3.0.2
      decimal.js: 10.5.0
      domexception: 4.0.0
      escodegen: 2.1.0
      form-data: 4.0.2
      html-encoding-sniffer: 3.0.0
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 7.3.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 4.1.4
      w3c-xmlserializer: 4.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 2.0.0
      whatwg-mimetype: 3.0.0
      whatwg-url: 11.0.0
      ws: 8.18.2
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@0.5.0: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonwebtoken@9.0.2:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 7.7.2

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  jwa@1.4.2:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jws@3.2.2:
    dependencies:
      jwa: 1.4.2
      safe-buffer: 5.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  kleur@3.0.3: {}

  klona@2.0.6: {}

  language-subtag-registry@0.3.23: {}

  language-tags@1.0.9:
    dependencies:
      language-subtag-registry: 0.3.23

  launch-editor@2.10.0:
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.2

  leven@3.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lines-and-columns@1.2.4: {}

  lint-staged@11.2.6:
    dependencies:
      cli-truncate: 2.1.0
      colorette: 1.4.0
      commander: 8.3.0
      cosmiconfig: 7.1.0
      debug: 4.4.1(supports-color@8.1.1)
      enquirer: 2.4.1
      execa: 5.1.1
      listr2: 3.14.0(enquirer@2.4.1)
      micromatch: 4.0.8
      normalize-path: 3.0.0
      please-upgrade-node: 3.2.0
      string-argv: 0.3.1
      stringify-object: 3.3.0
      supports-color: 8.1.1

  listr2@3.14.0(enquirer@2.4.1):
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.20
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.4.1
      rxjs: 7.8.2
      through: 2.3.8
      wrap-ansi: 7.0.0
    optionalDependencies:
      enquirer: 2.4.1

  loader-runner@4.3.0: {}

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.includes@4.3.0: {}

  lodash.isboolean@3.0.3: {}

  lodash.isinteger@4.0.4: {}

  lodash.isnumber@3.0.3: {}

  lodash.isplainobject@4.0.6: {}

  lodash.isstring@4.0.1: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.once@4.1.1: {}

  lodash@4.17.21: {}

  log-update@4.0.0:
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lz-string@1.5.0: {}

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  make-error@1.3.6: {}

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  math-intrinsics@1.1.0: {}

  media-typer@0.3.0: {}

  memfs@3.6.0:
    dependencies:
      fs-monkey: 1.0.6

  memoize-one@5.2.1: {}

  merge-descriptors@1.0.3: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  methods@1.1.2: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@2.1.0: {}

  min-indent@1.0.1: {}

  mini-css-extract-plugin@1.6.2(webpack@5.99.9):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.99.9(webpack-cli@4.10.0)
      webpack-sources: 1.4.3

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  mkdirp@1.0.4: {}

  moo-color@1.0.3:
    dependencies:
      color-name: 1.1.4

  mrmime@2.0.1: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  multicast-dns@7.2.5:
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-addon-api@7.1.1:
    optional: true

  node-forge@1.3.1: {}

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nwsapi@2.2.20: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-object-atoms: 1.1.1

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  obuf@1.1.2: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  opener@1.5.2: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  os-homedir@1.0.2: {}

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-map@2.1.0: {}

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-try@2.2.0: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@7.3.0:
    dependencies:
      entities: 6.0.0

  parseurl@1.3.3: {}

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-is-inside@1.0.2: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@0.1.12: {}

  path-type@4.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pify@4.0.1: {}

  pinkie-promise@2.0.1:
    dependencies:
      pinkie: 2.0.4

  pinkie@2.0.4: {}

  pirates@4.0.7: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  please-upgrade-node@3.2.0:
    dependencies:
      semver-compare: 1.0.0

  portfinder@1.0.37:
    dependencies:
      async: 3.2.6
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  possible-typed-array-names@1.1.0: {}

  postcss-modules-extract-imports@3.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-modules-local-by-default@4.2.0(postcss@8.5.3):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.2.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0

  postcss-modules-values@4.0.0(postcss@8.5.3):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      postcss: 8.5.3

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier@3.5.3: {}

  pretty-error@4.0.0:
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0

  pretty-format@26.6.2:
    dependencies:
      '@jest/types': 26.6.2
      ansi-regex: 5.0.1
      ansi-styles: 4.3.0
      react-is: 17.0.2

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  prism-react-renderer@1.3.5(react@17.0.1):
    dependencies:
      react: 17.0.1

  process-nextick-args@2.0.1: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rc-slider@11.1.8(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  rc-util@5.44.4(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-is: 18.3.1

  react-arborist@3.4.3(@types/node@22.15.21)(@types/react@17.0.1)(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      react: 17.0.1
      react-dnd: 14.0.5(@types/node@22.15.21)(@types/react@17.0.1)(react@17.0.1)
      react-dnd-html5-backend: 14.1.0
      react-dom: 17.0.1(react@17.0.1)
      react-window: 1.8.11(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      redux: 5.0.1
      use-sync-external-store: 1.5.0(react@17.0.1)
    transitivePeerDependencies:
      - '@types/hoist-non-react-statics'
      - '@types/node'
      - '@types/react'

  react-clientside-effect@1.2.8(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      react: 17.0.1

  react-dnd-html5-backend@14.1.0:
    dependencies:
      dnd-core: 14.0.1

  react-dnd@14.0.5(@types/node@22.15.21)(@types/react@17.0.1)(react@17.0.1):
    dependencies:
      '@react-dnd/invariant': 2.0.0
      '@react-dnd/shallowequal': 2.0.0
      dnd-core: 14.0.1
      fast-deep-equal: 3.1.3
      hoist-non-react-statics: 3.3.2
      react: 17.0.1
    optionalDependencies:
      '@types/node': 22.15.21
      '@types/react': 17.0.1

  react-dom@17.0.1(react@17.0.1):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react: 17.0.1
      scheduler: 0.20.2

  react-draggable@4.4.6(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  react-error-boundary@3.1.4(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      react: 17.0.1

  react-focus-lock@2.13.6(@types/react@17.0.1)(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      focus-lock: 1.3.6
      prop-types: 15.8.1
      react: 17.0.1
      react-clientside-effect: 1.2.8(react@17.0.1)
      use-callback-ref: 1.3.3(@types/react@17.0.1)(react@17.0.1)
      use-sidecar: 1.1.3(@types/react@17.0.1)(react@17.0.1)
    optionalDependencies:
      '@types/react': 17.0.1

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-lifecycles-compat@3.0.4: {}

  react-live@2.4.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      '@types/buble': 0.20.5
      buble: 0.19.6
      core-js: 3.42.0
      dom-iterator: 1.0.2
      prism-react-renderer: 1.3.5(react@17.0.1)
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-simple-code-editor: 0.11.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1)
      unescape: 1.0.1

  react-redux@7.2.2(react-dom@17.0.1(react@17.0.1))(react@17.0.1)(redux@4.0.5):
    dependencies:
      '@babel/runtime': 7.27.1
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 17.0.1
      react-is: 16.13.1
      redux: 4.0.5
    optionalDependencies:
      react-dom: 17.0.1(react@17.0.1)

  react-refresh-typescript@2.0.10(react-refresh@0.11.0)(typescript@5.8.3):
    dependencies:
      react-refresh: 0.11.0
      typescript: 5.8.3

  react-refresh@0.11.0: {}

  react-router-dom@6.30.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-router: 6.30.1(react@17.0.1)

  react-router@6.30.1(react@17.0.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 17.0.1

  react-simple-code-editor@0.11.3(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  react-style-tag@3.0.1(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      stylis: 4.3.6

  react-transition-group@2.9.0(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      dom-helpers: 3.4.0
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-lifecycles-compat: 3.0.4

  react-window@1.8.11(react-dom@17.0.1(react@17.0.1))(react@17.0.1):
    dependencies:
      '@babel/runtime': 7.27.1
      memoize-one: 5.2.1
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)

  react@17.0.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  rechoir@0.7.1:
    dependencies:
      resolve: 1.22.10

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  redux-thunk@2.4.2(redux@4.2.1):
    dependencies:
      redux: 4.2.1

  redux@4.0.5:
    dependencies:
      loose-envify: 1.4.0
      symbol-observable: 1.2.0

  redux@4.2.1:
    dependencies:
      '@babel/runtime': 7.27.1

  redux@5.0.1: {}

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@9.0.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpu-core@4.8.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 9.0.0
      regjsgen: 0.5.2
      regjsparser: 0.7.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.5.2: {}

  regjsparser@0.7.0:
    dependencies:
      jsesc: 0.5.0

  relateurl@0.2.7: {}

  renderkid@3.0.0:
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  reselect@4.1.8: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve.exports@2.0.3: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  robust-predicates@3.0.2: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rupture-sass@0.3.0: {}

  rw@1.3.3: {}

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  sass-loader@11.1.1(sass@1.89.0)(webpack@5.99.9):
    dependencies:
      klona: 2.0.6
      neo-async: 2.6.2
      webpack: 5.99.9(webpack-cli@4.10.0)
    optionalDependencies:
      sass: 1.89.0

  sass@1.89.0:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.2
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.20.2:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  schema-utils@3.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@4.3.2:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  secure-compare@3.0.1: {}

  select-hose@2.0.0: {}

  selfsigned@2.4.1:
    dependencies:
      '@types/node-forge': 1.3.11
      node-forge: 1.3.1

  semver-compare@1.0.0: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@5.0.1:
    dependencies:
      randombytes: 2.1.0

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.2: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  sirv@2.0.4:
    dependencies:
      '@polka/url': 1.0.0-next.29
      mrmime: 2.0.1
      totalist: 3.0.1

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  slice-ansi@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  source-list-map@2.0.1: {}

  source-map-js@1.2.1: {}

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  sourcemap-codec@1.4.8: {}

  spdy-transport@3.0.0:
    dependencies:
      debug: 4.4.1
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2:
    dependencies:
      debug: 4.4.1
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  sprintf-js@1.0.3: {}

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  stackframe@1.3.4: {}

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  string-argv@0.3.1: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.includes@2.0.1:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.10

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.10

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.10
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  stringify-object@3.3.0:
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  style-loader@2.0.0(webpack@5.99.9):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.99.9(webpack-cli@4.10.0)

  styled-components@5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1):
    dependencies:
      '@babel/helper-module-imports': 7.27.1(supports-color@5.5.0)
      '@babel/traverse': 7.27.1(supports-color@5.5.0)
      '@emotion/is-prop-valid': 0.8.8
      '@emotion/stylis': 0.8.5
      '@emotion/unitless': 0.7.5
      babel-plugin-styled-components: 2.1.4(@babel/core@7.27.1)(styled-components@5.2.1(@babel/core@7.27.1)(react-dom@17.0.1(react@17.0.1))(react-is@18.3.1)(react@17.0.1))(supports-color@5.5.0)
      css-to-react-native: 3.2.0
      hoist-non-react-statics: 3.3.2
      react: 17.0.1
      react-dom: 17.0.1(react@17.0.1)
      react-is: 18.3.1
      shallowequal: 1.1.0
      supports-color: 5.5.0
    transitivePeerDependencies:
      - '@babel/core'

  stylis@4.3.6: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  symbol-observable@1.2.0: {}

  symbol-tree@3.2.4: {}

  tapable@2.2.2: {}

  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.39.2
      webpack: 5.99.9(webpack-cli@4.10.0)

  terser@5.39.2:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.1
      commander: 2.20.3
      source-map-support: 0.5.21

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-table@0.2.0: {}

  through@2.3.8: {}

  thunky@1.1.0: {}

  tmpl@1.0.5: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  totalist@3.0.1: {}

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@3.0.0:
    dependencies:
      punycode: 2.3.1

  ts-api-utils@1.4.3(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  ts-jest@29.3.4(@babel/core@7.27.1)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.1))(jest@29.7.0(@types/node@22.15.21))(typescript@5.8.3):
    dependencies:
      bs-logger: 0.2.6
      ejs: 3.1.10
      fast-json-stable-stringify: 2.1.0
      jest: 29.7.0(@types/node@22.15.21)
      jest-util: 29.7.0
      json5: 2.2.3
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.7.2
      type-fest: 4.41.0
      typescript: 5.8.3
      yargs-parser: 21.1.1
    optionalDependencies:
      '@babel/core': 7.27.1
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.27.1)

  ts-loader@9.5.2(typescript@5.8.3)(webpack@5.99.9):
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.18.1
      micromatch: 4.0.8
      semver: 7.7.2
      source-map: 0.7.4
      typescript: 5.8.3
      webpack: 5.99.9(webpack-cli@4.10.0)

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@5.8.3):
    dependencies:
      tslib: 1.14.1
      typescript: 5.8.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-fest@4.41.0: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript@5.8.3: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.21.0: {}

  unescape@1.0.1:
    dependencies:
      extend-shallow: 2.0.1

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  union@0.5.0:
    dependencies:
      qs: 6.14.0

  universalify@0.2.0: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.5):
    dependencies:
      browserslist: 4.24.5
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-join@2.0.5: {}

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  url@0.11.4:
    dependencies:
      punycode: 1.4.1
      qs: 6.14.0

  use-callback-ref@1.3.3(@types/react@17.0.1)(react@17.0.1):
    dependencies:
      react: 17.0.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 17.0.1

  use-sidecar@1.1.3(@types/react@17.0.1)(react@17.0.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 17.0.1
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 17.0.1

  use-sync-external-store@1.5.0(react@17.0.1):
    dependencies:
      react: 17.0.1

  util-deprecate@1.0.2: {}

  util@0.12.5:
    dependencies:
      inherits: 2.0.4
      is-arguments: 1.2.0
      is-generator-function: 1.1.0
      is-typed-array: 1.1.15
      which-typed-array: 1.1.19

  utila@0.4.0: {}

  utils-merge@1.0.1: {}

  uuid@8.3.2: {}

  v8-to-istanbul@9.3.0:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 2.0.0

  vary@1.1.2: {}

  vlq@1.0.1: {}

  w3c-xmlserializer@4.0.0:
    dependencies:
      xml-name-validator: 4.0.0

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  watchpack@2.4.4:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  webidl-conversions@7.0.0: {}

  webpack-bundle-analyzer@4.10.2:
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      acorn: 8.14.1
      acorn-walk: 8.3.4
      commander: 7.2.0
      debounce: 1.2.1
      escape-string-regexp: 4.0.0
      gzip-size: 6.0.0
      html-escaper: 2.0.2
      opener: 1.5.2
      picocolors: 1.1.1
      sirv: 2.0.4
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  webpack-cli@4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9):
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 1.2.0(webpack-cli@4.10.0)(webpack@5.99.9)
      '@webpack-cli/info': 1.5.0(webpack-cli@4.10.0)
      '@webpack-cli/serve': 1.7.0(webpack-cli@4.10.0)(webpack-dev-server@4.15.2)
      colorette: 2.0.20
      commander: 7.2.0
      cross-spawn: 7.0.6
      fastest-levenshtein: 1.0.16
      import-local: 3.2.0
      interpret: 2.2.0
      rechoir: 0.7.1
      webpack: 5.99.9(webpack-cli@4.10.0)
      webpack-merge: 5.10.0
    optionalDependencies:
      webpack-bundle-analyzer: 4.10.2
      webpack-dev-server: 4.15.2(webpack-cli@4.10.0)(webpack@5.99.9)

  webpack-dev-middleware@5.3.4(webpack@5.99.9):
    dependencies:
      colorette: 2.0.20
      memfs: 3.6.0
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.3.2
      webpack: 5.99.9(webpack-cli@4.10.0)

  webpack-dev-server@4.15.2(webpack-cli@4.10.0)(webpack@5.99.9):
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.22
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.7
      '@types/sockjs': 0.3.36
      '@types/ws': 8.18.1
      ansi-html-community: 0.0.8
      bonjour-service: 1.3.0
      chokidar: 3.6.0
      colorette: 2.0.20
      compression: 1.8.0
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.21.2
      graceful-fs: 4.2.11
      html-entities: 2.6.0
      http-proxy-middleware: 2.0.9(@types/express@4.17.22)
      ipaddr.js: 2.2.0
      launch-editor: 2.10.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.3.2
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack-dev-middleware: 5.3.4(webpack@5.99.9)
      ws: 8.18.2
    optionalDependencies:
      webpack: 5.99.9(webpack-cli@4.10.0)
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  webpack-merge@5.10.0:
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1

  webpack-sources@1.4.3:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack-sources@3.3.0: {}

  webpack@5.99.9(webpack-cli@4.10.0):
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.14.1
      browserslist: 4.24.5
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14(webpack@5.99.9)
      watchpack: 2.4.4
      webpack-sources: 3.3.0
    optionalDependencies:
      webpack-cli: 4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9)
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.10
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  whatwg-encoding@2.0.0:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-fetch@3.6.2: {}

  whatwg-mimetype@3.0.0: {}

  whatwg-url@11.0.0:
    dependencies:
      tr46: 3.0.0
      webidl-conversions: 7.0.0

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wildcard@2.0.1: {}

  word-wrap@1.2.5: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  ws@7.5.10: {}

  ws@8.18.2: {}

  xml-name-validator@4.0.0: {}

  xml@1.0.1: {}

  xmlchars@2.2.0: {}

  xss@1.0.15:
    dependencies:
      commander: 2.20.3
      cssfilter: 0.0.10

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@1.10.2: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}
