import React from 'react';
import { render } from '@testing-library/react';
import { HighlightedIcon } from './HighlightedIcon';

describe('HighlightedIcon', () => {
  it('renders with basic props', () => {
    const testId = 'test-highlighted-icon';
    const { getByTestId } = render(<HighlightedIcon testId={testId} id="test-id" iconName="home" fill="blue" />);

    expect(getByTestId(testId)).toBeInTheDocument();
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<HighlightedIcon ref={ref} id="test-id" iconName="home" fill="blue" />);

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });
});
