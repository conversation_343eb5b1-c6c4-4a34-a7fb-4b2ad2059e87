/* eslint-disable no-extend-native */
import { IEmployeeSkill } from '@models/qualifications';
import { mapLegacySkillsToTableVM } from './legacy-skills-utils';

describe('Legacy Skills Utils', () => {
  const mockDate = new Date('2025-06-16T12:00:00Z');
  const realDate = Date;
  const realToLocaleDateString = Date.prototype.toLocaleDateString;

  beforeAll(() => {
    global.Date = class extends Date {
      constructor(date?: string | number | Date) {
        if (date) {
          super(date);
        } else {
          super(mockDate);
        }
      }

      toLocaleDateString() {
        return realToLocaleDateString.call(this, 'en-US', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
          timeZone: 'UTC',
        });
      }
    } as DateConstructor;
  });

  afterAll(() => {
    global.Date = realDate;
    Date.prototype.toLocaleDateString = realToLocaleDateString;
  });

  describe('mapLegacySkillsToTableVM', () => {
    const mockSkills: IEmployeeSkill[] = [
      {
        Id: 1,
        SkillName: 'JavaScript',
        SkillLevelName: 'Advanced',
        ExpirationDate: '2025-12-31T00:00:00Z',
        DateLastAcquired: '2023-01-15T00:00:00Z',
        LastAssignedBy: 'John Doe',
      },
      {
        Id: 2,
        SkillName: 'TypeScript',
        SkillLevelName: 'Intermediate',
        ExpirationDate: null,
        DateLastAcquired: '2023-06-01T00:00:00Z',
        LastAssignedBy: null,
      },
    ] as IEmployeeSkill[];

    it('maps skills data correctly', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 1,
        skill: 'JavaScript',
        skillLevel: 'Advanced',
        expirationDate: '12/31/2025',
        dateLastAcquired: '1/15/2023',
        lastAssignedBy: 'John Doe',
      });
    });

    it('handles missing expiration date', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);
      expect(result[1].expirationDate).toBe('N/A');
    });

    it('handles missing lastAssignedBy', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);
      expect(result[1].lastAssignedBy).toBe('Unknown');
    });

    it('formats dates consistently', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);

      const datePattern = /^(?:1[0-2]|[1-9])\/(?:3[01]|[12][0-9]|[1-9])\/\d{4}$/;

      expect(result[0].dateLastAcquired).toMatch(datePattern);
      expect(result[0].expirationDate).toMatch(datePattern);
    });

    it('handles empty skills array', () => {
      const result = mapLegacySkillsToTableVM([]);
      expect(result).toEqual([]);
    });

    it('preserves skill order', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);

      expect(result.map((item) => item.skill)).toEqual(['JavaScript', 'TypeScript']);
    });

    it('maps all required fields', () => {
      const result = mapLegacySkillsToTableVM(mockSkills);

      const expectedKeys = ['id', 'skill', 'skillLevel', 'expirationDate', 'dateLastAcquired', 'lastAssignedBy'];

      expect(Object.keys(result[0])).toEqual(expect.arrayContaining(expectedKeys));
    });

    it('handles invalid date inputs', () => {
      const invalidSkill = {
        ...mockSkills[0],
        DateLastAcquired: 'invalid-date',
      };

      expect(() => mapLegacySkillsToTableVM([invalidSkill])).not.toThrow();
    });
  });
});
