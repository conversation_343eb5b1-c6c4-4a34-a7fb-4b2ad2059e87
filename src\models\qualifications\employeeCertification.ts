export interface IEmployeeCertification {
  Id: number;
  LMSCertificationId: number;
  LMSEmployeeCertificationId: number;
  EmployeeId: number;
  DateFirstAcquired: string;
  DateLastAcquired: string;
  ExpirationDate: string | null;
  DaysUntilExpiration: number | null;
  AssignedByUserId: number;
  TrainingProgramId: number | null;
  CourseId: number | null;
  LastAssignedBy: string;
  LMSAssignmentMethodId: number;
  CertificationAssignmentMethodCode: string;
  HasRequiredCourses: boolean;
  CertificationShortName: string;
  LMSEmployeeCertifications: any | null;
  LMSCourseCertificationId: number | null;
  LMSLearningPlanCertificationId: number | null;
  LMSCertificationStatus: string;
  LMSCertificationStatusId: number;
  LMSCertificationStatusXRefCode: string;
  CertificationExpirationThreshold: number;
  NumberOfNotes: number | null;
  IsExternal: boolean;
  ClientEntityId: number | null;
  EntityState: number;
  LastModifiedTimestamp: string | null;
  LocalizedName: string;
  LocalizedDescription: string;
  PrimaryKeyId: number | null;
  OriginalValues: any | null;
  ExtendedProperties: any[];
}
