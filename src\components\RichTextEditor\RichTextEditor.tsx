import React, { useRef, useImperativeHandle, useEffect } from 'react';
import { RichTextEditor as EverestRichTextEditor } from '@ceridianhcm/everest-community';

import './rich-text-editor.scss';

/**
 * Props for RichTextEditor component - Simplified interface focusing on core functionality
 */
export interface RichTextEditorProps {
  /** Unique field identifier */
  id: string;
  /** Field label */
  label?: string;
  /** Current field value */
  value?: string;
  /** Callback when field value changes */
  onChange?: (value: string) => void;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Whether the field is read-only */
  readOnly?: boolean;
  /** Whether the field is required */
  required?: boolean;
  /** Field status for styling */
  status?: 'default' | 'error';
  /** Status message to display */
  statusMessage?: string;
  /** Helper text to display below the field */
  helperText?: string;
  /** Character limit for display */
  charLimit?: number;
  /** Alternative text for character limit display */
  charLimitAltText?: string;
  /** Additional CSS class name */
  className?: string;
  /** Component variant for different styling */
  variant?: 'default' | 'smart' | 'compact';
  /** Whether the component should take full width */
  fullWidth?: boolean;
  /** Spacing variant for layout control */
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Callback when field receives focus */
  onFocus?: (event: React.FocusEvent<HTMLElement>) => void;
  /** Callback when field loses focus */
  onBlur?: (event: React.FocusEvent<HTMLElement>) => void;
  /** Test ID for testing purposes */
  testId?: string;
  /** ARIA label for accessibility */
  ariaLabel?: string;
  /** Name attribute for form field */
  name?: string;
}

/**
 * RichTextEditor is a composable wrapper around the Everest Community RichTextEditor component.
 *
 * This component provides a clean, reusable interface for rich text editing without any
 * form-specific logic. It can be used standalone or as a building block for more
 * complex components like form-integrated wrappers. It follows the HR Profile UI
 * coding standards and provides a consistent API for rich text input functionality.
 *
 * Uses the RichTextEditor component from @ceridianhcm/everest-community for enhanced
 * rich text editing capabilities.
 *
 * @example
 * ```tsx
 * const [value, setValue] = useState('');
 *
 * <RichTextEditor
 *   id="description"
 *   label="Description"
 *   value={value}
 *   onChange={setValue}
 *   charLimit={500}
 *   helperText="Enter a detailed description"
 * />
 * ```
 *
 * @example
 * ```tsx
 * // With error state
 * <RichTextEditor
 *   id="bio"
 *   label="Biography"
 *   value={bio}
 *   onChange={setBio}
 *   status="error"
 *   statusMessage="Biography is required"
 *   charLimit={1000}
 * />
 * ```
 *
 * @param props - The component props
 * @returns JSX.Element - The rendered RichTextEditor component
 */
export const RichTextEditor = React.forwardRef<HTMLTextAreaElement, RichTextEditorProps>(
  (
    {
      id,
      label,
      value,
      onChange,
      disabled = false,
      readOnly = false,
      required = false,
      status = 'default',
      statusMessage,
      helperText,
      charLimit,
      charLimitAltText,
      className,
      variant = 'default',
      fullWidth = false,
      spacing,
      onFocus,
      onBlur,
      testId,
      ariaLabel,
      name,
    },
    ref,
  ) => {
    // Create internal ref to access the textarea element
    const internalRef = useRef<HTMLTextAreaElement>(null);

    // Use useImperativeHandle to expose the textarea element through the forwarded ref
    useImperativeHandle(ref, () => internalRef.current as HTMLTextAreaElement, []);

    // Use useEffect to find and assign the actual textarea element
    useEffect(() => {
      if (id) {
        const textareaElement = document.getElementById(id) as HTMLTextAreaElement;
        if (textareaElement && internalRef.current !== textareaElement) {
          (internalRef as any).current = textareaElement;
        }
      }
    }, [id]);

    // Simple wrapper function to handle onChange
    const handleChange = (event: any) => {
      if (onChange) {
        onChange(event);
      }
    };

    // Wrapper functions to handle event type conversion
    const handleFocus = (event: any) => {
      if (onFocus) {
        // Create a synthetic focus event for compatibility
        const syntheticEvent = {
          ...event,
          currentTarget: event.target || event.currentTarget,
          target: event.target || event.currentTarget,
        } as React.FocusEvent<HTMLElement>;
        onFocus(syntheticEvent);
      }
    };

    const handleBlur = (event: any) => {
      if (onBlur) {
        // Create a synthetic blur event for compatibility
        const syntheticEvent = {
          ...event,
          currentTarget: event.target || event.currentTarget,
          target: event.target || event.currentTarget,
        } as React.FocusEvent<HTMLElement>;
        onBlur(syntheticEvent);
      }
    };

    // Build CSS classes based on component state
    const cssClasses = [
      'rich-text-editor',
      disabled && 'rich-text-editor--disabled',
      readOnly && 'rich-text-editor--read-only',
      status === 'error' && 'rich-text-editor--error',
      variant !== 'default' && `rich-text-editor--${variant}`,
      fullWidth && 'rich-text-editor--full-width',
      spacing && `rich-text-editor--spacing-${spacing}`,
      className,
    ]
      .filter(Boolean)
      .join(' ');

    // Character counter display logic
    const getCharacterCounterText = () => {
      if (!charLimit) return null;
      const currentLength = (value || '').length;
      return charLimitAltText || `${currentLength}/${charLimit}`;
    };

    return (
      <div className={cssClasses} data-testid={testId}>
        {label && <div className="rich-text-editor__label">{label}</div>}
        <EverestRichTextEditor
          id={id}
          value={value}
          onChange={handleChange}
          disabled={disabled}
          {...(readOnly && { readOnly })}
          {...(required && { required })}
          {...(name && { name })}
          {...(ariaLabel && { 'aria-label': ariaLabel })}
          {...(testId && { 'data-testid': `${testId}-input` })}
          {...(variant !== 'default' && { 'data-variant': variant })}
          {...(onFocus && { onFocus: handleFocus })}
          {...(onBlur && { onBlur: handleBlur })}
        />
        {helperText && <div className="rich-text-editor__helper">{helperText}</div>}
        {status === 'error' && statusMessage && (
          <div className="rich-text-editor__error" role="alert">
            {statusMessage}
          </div>
        )}
        {charLimit && <div className="rich-text-editor__counter">{getCharacterCounterText()}</div>}
      </div>
    );
  },
);

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
