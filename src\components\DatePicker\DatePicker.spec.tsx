import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DatePicker } from './DatePicker';

jest.mock('@ceridianhcm/components', () => {
  const actual = jest.requireActual('@ceridianhcm/components');
  return {
    ...actual,
    DatePicker: ({ id, value, label, onChange }: any) => {
      return (
        <input
          data-testid={id}
          aria-label={label}
          value={value ? value.toString() : ''}
          onChange={(event) => onChange?.(event.target.value, new Date('2023-01-01'))}
        />
      );
    },
  };
});

describe('DatePicker Component', () => {
  const baseProps = {
    id: 'test-date-picker',
    label: 'Test Date',
  } as const;

  it('renders with required props', () => {
    render(<DatePicker {...baseProps} />);
    expect(screen.getByTestId(baseProps.id)).toBeInTheDocument();
  });

  it('renders with a predefined value', () => {
    const value = new Date(2024, 0, 1);
    render(<DatePicker {...baseProps} value={value} />);
    const input = screen.getByTestId(baseProps.id) as HTMLInputElement;
    expect(input.value).toBe(value.toString());
  });

  it('calls onChange handler when date is changed', () => {
    const handleChange = jest.fn();
    render(<DatePicker {...baseProps} onChange={handleChange} />);

    const input = screen.getByTestId(baseProps.id);
    fireEvent.change(input, { target: { value: '01/01/2023' } });

    // DatePicker wrapper should forward only the Date value
    expect(handleChange).toHaveBeenCalledWith(new Date('2023-01-01'));
  });
});
