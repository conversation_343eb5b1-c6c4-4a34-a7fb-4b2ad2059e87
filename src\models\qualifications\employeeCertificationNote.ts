import { IEmployeeCertificationAttachment } from './employeeCertificationAttachment';

export interface IEmployeeCertificationNote {
  LMSEmployeeCertificationNoteId: number;
  LMSEmployeeCertificationId: number;
  Attachments: IEmployeeCertificationAttachment[] | null;
  EmployeeId: number;
  Title: string | null;
  Comment: string;
  CreatedDate: string;
  CreatedTimestampString: string;
  CreatedUserDisplayName: string;
  CreatedUserId: number;
  CanViewCreatedUser: boolean;
  LastModifiedUserId: number;
  LastModifiedDisplayName: string;
  CanViewLastModifiedUser: boolean;
  LastModifiedDate: string;
  LastModifiedTimestampString: string;
  ClientEntityId: number | null;
  EntityState: number;
  LastModifiedTimestamp: string | null;
  LocalizedName: string;
  LocalizedDescription: string;
  PrimaryKeyId: number | null;
  OriginalValues: any;
  ExtendedProperties: any[];
}

export interface EntityList<T> {
  Total: number | null;
  Type: string;
  Entities: T[];
}

export interface IEmployeeCertificationNoteResponse {
  Success: boolean;
  ErrorMessage: string | null;
  EntityLists: EntityList<IEmployeeCertificationNote>[];
}
