import { formatDate } from './dateUtils';

describe('dateUtils', () => {
  describe('formatDate', () => {
    it('formats date with default en-US locale', () => {
      const result = formatDate('2024-01-15T10:30:00');
      expect(result).toBe('Jan 15, 2024');
    });

    it('formats date with explicit en-US locale', () => {
      const result = formatDate('2024-01-15T10:30:00', 'en-US');
      expect(result).toBe('Jan 15, 2024');
    });

    it('formats date with different locale (fr-FR)', () => {
      const result = formatDate('2024-01-15T10:30:00', 'fr-FR');
      expect(result).toBe('15 janv. 2024');
    });

    it('formats date with different locale (de-DE)', () => {
      const result = formatDate('2024-01-15T10:30:00', 'de-DE');
      expect(result).toBe('15. Jan. 2024');
    });

    it('formats date with different locale (es-ES)', () => {
      const result = formatDate('2024-01-15T10:30:00', 'es-ES');
      expect(result).toBe('15 ene 2024');
    });

    it('handles different date formats', () => {
      const isoDate = formatDate('2024-12-25T12:00:00.000Z');
      const simpleDate = formatDate('2024-12-25T12:00:00');

      expect(isoDate).toBe('Dec 25, 2024');
      expect(simpleDate).toBe('Dec 25, 2024');
    });

    it('handles edge case dates', () => {
      const leapYear = formatDate('2024-02-29T12:00:00');
      const endOfYear = formatDate('2024-12-31T12:00:00');
      const startOfYear = formatDate('2024-01-01T12:00:00');

      expect(leapYear).toBe('Feb 29, 2024');
      expect(endOfYear).toBe('Dec 31, 2024');
      expect(startOfYear).toBe('Jan 1, 2024');
    });

    it('handles different years', () => {
      const pastYear = formatDate('2020-06-15T12:00:00');
      const futureYear = formatDate('2030-06-15T12:00:00');

      expect(pastYear).toBe('Jun 15, 2020');
      expect(futureYear).toBe('Jun 15, 2030');
    });

    it('handles single digit days and months', () => {
      const singleDigitDay = formatDate('2024-03-05T12:00:00');
      const singleDigitMonth = formatDate('2024-01-15T12:00:00');

      expect(singleDigitDay).toBe('Mar 5, 2024');
      expect(singleDigitMonth).toBe('Jan 15, 2024');
    });

    it('returns "Invalid Date" for invalid date string', () => {
      const result = formatDate('invalid-date');
      expect(result).toBe('Invalid Date');
    });

    it('returns "Invalid Date" for empty string', () => {
      const result = formatDate('');
      expect(result).toBe('Invalid Date');
    });

    it('returns "Invalid Date" for null date', () => {
      const result = formatDate(null as any);
      expect(result).toBe('Invalid Date');
    });

    it('returns "Invalid Date" for undefined date', () => {
      const result = formatDate(undefined as any);
      expect(result).toBe('Invalid Date');
    });
  });
});
