.full-width-container {
  display: flex;
  align-items: center;
  width: 100%;

  &.fill-children {
    > * {
      width: 100%;
      flex: 1 1 auto;
    }
  }

  // Preset min width variants
  &.width-default {
    min-width: 1216px;
  }

  &.width-full {
    min-width: 100%;
  }

  &.width-unset {
    min-width: unset;
  }

  // Preset max width variants
  &.max-width-default {
    max-width: 1216px;
  }

  &.max-width-full {
    max-width: 100%;
  }

  &.max-width-unset {
    max-width: unset;
  }

  // Padding variants
  &.padding-sm {
    padding: 0 var(--evr-spacing-sm, 0.75rem);
  }

  &.padding-md {
    padding: 0 var(--evr-spacing-md, 1rem);
  }

  &.padding-l {
    padding: 0 var(--evr-spacing-l, 1.5rem);
  }

  &.padding-xl {
    padding: 0 var(--evr-spacing-xl, 2.5rem);
  }

  &.padding-2xl {
    padding: 0 var(--evr-spacing-2xl, 3rem);
  }

  // Alignment variants
  &.align-start {
    align-items: flex-start;
  }

  &.align-center {
    align-items: center;
  }

  &.align-end {
    align-items: flex-end;
  }

  &.align-stretch {
    align-items: stretch;
  }

  &.align-baseline {
    align-items: baseline;
  }
}
