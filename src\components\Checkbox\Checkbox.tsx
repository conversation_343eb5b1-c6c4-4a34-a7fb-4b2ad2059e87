import { Checkbox as EverestCheckbox, ICheckboxProps as IEverestCheckboxProps } from '@ceridianhcm/components';
import React from 'react';

export type TCheckedStateTypes = 'checked' | 'unchecked' | 'indeterminate';

/**
 * Helper function to convert CheckedStateTypes to boolean values
 * @param checkedState Check state type
 * @returns True if checked; False if unchecked; null by default
 */
export const GetCheckedState = (checkedState: TCheckedStateTypes) => {
  if (checkedState) {
    switch (checkedState) {
      case 'checked': {
        return true;
      }
      case 'unchecked': {
        return false;
      }
    }
  }

  return null;
};

export interface ICheckboxProps extends IEverestCheckboxProps {
  onCheckedStateChanged?: (checkedState: TCheckedStateTypes) => void;
}

const Checkbox: React.FC<ICheckboxProps> = (props) => {
  /**
   * Intermediate onClick helper to intercept default onClick and properly return the new Checkbox value based on checkedState prop for custom onCheckedStateChanged callback.
   * If no onCheckedStateChanged callback, default to onClick callback.
   * @param e Mouse event
   * @returns New checkbox value
   */
  const onClickHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (props.onCheckedStateChanged) {
      switch (props.checkedState) {
        case 'checked': {
          props.onCheckedStateChanged('unchecked');
          break;
        }
        case 'unchecked': {
          props.onCheckedStateChanged('checked');
          break;
        }
      }
    } else if (props.onChange) {
      props.onChange(event);
    }
  };

  return <EverestCheckbox onChange={onClickHandler} {...props} />;
};

export { Checkbox };
