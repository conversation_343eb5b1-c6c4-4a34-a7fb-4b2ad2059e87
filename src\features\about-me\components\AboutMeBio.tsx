import React from 'react';
import RichTextEditor from '../../../components/RichTextEditor';

interface AboutMeBioProps {
  /**
   * The current biography text value
   */
  value: string;
  /**
   * Callback function when the biography text changes
   */
  onChange: (value: string) => void;
  /**
   * Error message to display if validation fails
   */
  error?: string;
  /**
   * Whether the field is disabled
   */
  disabled?: boolean;
  /**
   * Additional CSS class name
   */
  className?: string;
}

/**
 * AboutMeBio component for editing user biography information
 * 
 * This component provides a rich text editor for the user's biography
 * with appropriate validation and character limits.
 */
export const AboutMeBio: React.FC<AboutMeBioProps> = ({
  value,
  onChange,
  error,
  disabled = false,
  className,
}) => {
  return (
    <RichTextEditor
      id="biography"
      label="Biography"
      value={value}
      onChange={onChange}
      disabled={disabled}
      status={error ? 'error' : 'default'}
      statusMessage={error}
      helperText="Share a brief professional summary"
      charLimit={500}
      className={className}
      testId="biography-field"
    />
  );
};

export default AboutMeBio;