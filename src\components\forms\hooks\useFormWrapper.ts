import { useForm, UseFormReturn, FieldValues, SubmitHandler, DefaultValues } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ZodSchema } from 'zod';
import { SidePanelButtonType, ISidePanelButtons } from '@components/SidePanel';
import { BaseSyntheticEvent } from 'react';

interface UseFormWrapperProps<T extends FieldValues> {
  id: string;
  schema: ZodSchema<T>;
  defaultValues: DefaultValues<T>;
  onSubmit: SubmitHandler<T>;
  onCancel?: () => void;
  submitLabel?: string;
  cancelLabel?: string;
  isLoading?: boolean;
}

interface UseFormWrapperReturn<T extends FieldValues> {
  methods: UseFormReturn<T>;
  actionButtons: ISidePanelButtons;
  handleSubmit: (e?: BaseSyntheticEvent) => Promise<void>;
  handleCancel?: () => void;
}

export const useFormWrapper = <T extends FieldValues>({
  id,
  schema,
  defaultValues,
  onSubmit,
  onCancel,
  submitLabel = 'Save changes',
  cancelLabel = 'Cancel',
  isLoading = false,
}: UseFormWrapperProps<T>): UseFormWrapperReturn<T> => {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onBlur',
  });

  const {
    formState: { isSubmitting, isDirty, isValid },
  } = methods;

  const handleSubmit = async (data: T) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
      throw error; // Re-throw to allow handling by the caller
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const isFormDisabled = isLoading || isSubmitting;
  const canSubmit = isDirty && isValid && !isFormDisabled;

  const actionButtons: ISidePanelButtons = {
    [SidePanelButtonType.Secondary]: onCancel
      ? {
          id: `${id}-cancel-button`,
          label: cancelLabel,
          onClick: handleCancel,
          disabled: isFormDisabled,
          type: 'button',
          size: 'large',
        }
      : undefined,
    [SidePanelButtonType.Primary]: {
      id: `${id}-submit-button`,
      label: isLoading ? 'Loading...' : submitLabel,
      onClick: methods.handleSubmit(handleSubmit),
      disabled: !canSubmit,
      type: 'submit',
      size: 'large',
      variant: 'primary',
    },
  };

  return {
    handleCancel: onCancel ? handleCancel : undefined,
    methods,
    actionButtons,
    handleSubmit: methods.handleSubmit(handleSubmit),
  };
};
