import React from 'react';
import { Avatar, TableCellLayout } from '@ceridianhcm/components';
import { sanitizeId } from '@utils/sanitizeUtils';

export const NameCell = (cellContext: any) => (
  <TableCellLayout flexDirection="row" flexAlign="center" flexGap>
    <Avatar
      id={`${sanitizeId(cellContext.rowData.DisplayName)}-avatar`}
      ariaLabel="Profile avatar image"
      size="sm"
      src={cellContext.rowData.PersonImage}
    />
    <TableCellLayout flexDirection="column">
      <span className="cell-text-dark">{cellContext.rowData.DisplayName}</span>
      <span className="evrBodyText2">{cellContext.rowData.PositionName}</span>
    </TableCellLayout>
  </TableCellLayout>
);
