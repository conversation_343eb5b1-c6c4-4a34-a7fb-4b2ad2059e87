import React from 'react';
import classNames from 'classnames';
import './full-width-wrapper.scss';

export type TPresetWidth = 'default' | 'full' | 'unset';
export type TAlignment = 'start' | 'center' | 'end' | 'stretch' | 'baseline';
export type TPadding = 'none' | 'sm' | 'md' | 'l' | 'xl' | '2xl';

export interface IFullWidthWrapperProps {
  /** Child elements to render */
  children: React.ReactNode;
  /** Custom class name for additional styling */
  className?: string;
  /** Sets the data-testid attribute */
  testId?: string;
  /** Minimum width - can be preset, custom value, or 'unset'
   * @default default (1216px) */
  minWidth?: TPresetWidth | string;
  /** Maximum width - can be preset, custom value, or 'unset'
   * @default default (1216px) */
  maxWidth?: TPresetWidth | string;
  /** Controls vertical alignment of children
   * @default center */
  alignItems?: TAlignment;
  /** @default none */
  verticalPadding?: TPadding | string;
  /** Controls horizontal padding of the wrapper
   * @default none */
  horizontalPadding?: TPadding | string;
  /** Controls if children should fill container width
   * @default false */
  fillChildren?: boolean;
}

export const FullWidthWrapper: React.FC<IFullWidthWrapperProps> = ({
  children,
  className,
  testId,
  minWidth = 'default',
  maxWidth = 'default',
  alignItems = 'center',
  verticalPadding = 'none',
  horizontalPadding = 'none',
  fillChildren = false,
}) => {
  const isPresetPadding = (p: string): p is TPadding => ['none', 'sm', 'md', 'l', 'xl', '2xl'].includes(p);

  const getPaddingValue = (padding: string) => {
    if (padding === 'none') return '0';
    return isPresetPadding(padding) ? `var(--evr-spacing-${padding})` : padding;
  };

  const style = {
    ...(minWidth !== 'default' && { minWidth }),
    ...(maxWidth !== 'default' && { maxWidth }),
    paddingTop: getPaddingValue(verticalPadding),
    paddingBottom: getPaddingValue(verticalPadding),
    paddingLeft: getPaddingValue(horizontalPadding),
    paddingRight: getPaddingValue(horizontalPadding),
  };

  const classes = classNames(
    'full-width-container',
    {
      [`align-${alignItems}`]: alignItems,
      'fill-children': fillChildren,
    },
    className,
  );

  return (
    <div className={classes} style={style} data-testid={testId}>
      {children}
    </div>
  );
};
