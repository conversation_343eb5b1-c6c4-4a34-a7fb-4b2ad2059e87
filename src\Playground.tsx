// eslint-disable react/jsx-wrap-multilines
import {
  Avatar,
  EverestProvider,
  MediaObject,
  SidePanel,
  SidePanelHeader,
  SidePanelBody,
  SidePanelFooter,
  Button,
} from '@ceridianhcm/components';
import { GlobalizationProvider } from '@ceridianhcm/react-core';
import React, { useEffect, useState, type FC } from 'react';
import { PageContent, Container, Accordion, RichTextEditor } from '@components/index';

import { useStore } from './hooks';
import { EmployeeStore } from './store';
import { IEmployeeProfileResult } from './models';
import '@ceridianhcm/theme/dist/theme.css'; // fonts and global typography styles

export const Playground: FC = () => {
  const [open, setOpen] = useState(false);
  const [sidePanelOpen, setSidePanelOpen] = useState(false);
  const [richTextValue, setRichTextValue] = useState('');
  const [contactInfo, setContactInfo] = useState<IEmployeeProfileResult>({} as IEmployeeProfileResult);

  const store = useStore(EmployeeStore);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await store.fetchEmployeeProfileInfo();
        if (response && response.Errors.length > 0) {
          console.error('Failed to fetch employee profile info. Errors:', response.ErrorMessage);
          return;
        }
        if (response && response.Result && '0' in response.Result) {
          const data = response.Result['0'];
          console.log(data);
          setContactInfo(data);
        } else {
          console.error('Unexpected data format for response.Result:', response.Result);
        }
      } catch (error) {
        console.error('Error fetching employee profile info:', error);
      }
    };

    fetchData();
  }, [store]);

  return (
    <EverestProvider id="hr-profile-ui">
      <GlobalizationProvider>
        <h1 className="evrHeading1" style={{ marginBottom: '2rem' }}>
          Profile UI Playground
        </h1>
        <h2 className="evrHeading2" style={{ marginBottom: '2rem' }}>
          content page wrapper cosumes the content
        </h2>
        <div
          style={{
            maxWidth: 1200,
            width: '100%',
            outline: '1px solid red',
            justifyContent: 'center',
            margin: '0 auto',
          }}
        >
          <PageContent id="example-page" testId="example-page">
            <div className="evrContentBlock">
              <h1>
                <span className="evrHeading1">Example Page Content</span>
              </h1>

              <MediaObject
                id="media-object-with-no-media"
                media={
                  <Avatar
                    id="size-avatar-lg"
                    ariaLabel="This is an Avatar with picture - size lg"
                    size="lg"
                    src="/assets/images/user-taro.jpg"
                  />
                }
                title={<h3 className="evrHeading3">{contactInfo?.DisplayName}</h3>}
                subtitle={<span className="evrBodyText2">{contactInfo?.Department}</span>}
                gap="--evr-spacing-sm"
              />
              <Container id="container-id" ariaLabel="Container Title" dataTestId="container-testid">
                <Container.Body>
                  <Accordion open={open} setOpen={setOpen} title="Accordion Title" id="accordion-id">
                    <p className="evrBodyText">This is the body content of the container.</p>
                    <Container id="container-id-2" ariaLabel="Container Title" dataTestId="container-testid">
                      <Container.Body>
                        <Container.Section>
                          <Container.Row label="Business" value="123 Business St, City, State, Zip" role="row" bold />
                          <Container.Row label="Personal" value="456 Personal Ave, City, State, Zip" role="row" />
                        </Container.Section>
                      </Container.Body>
                    </Container>
                  </Accordion>
                </Container.Body>
              </Container>

              {/* RichTextEditor Component Demo */}
              <div style={{ marginTop: '2rem' }}>
                <h2 className="evrHeading2" style={{ marginBottom: '1rem' }}>
                  RichTextEditor Component Demo
                </h2>

                <Button
                  id="open-rich-text-sidepanel"
                  label="Open RichTextEditor in SidePanel"
                  onClick={() => setSidePanelOpen(true)}
                />

                <div style={{ marginTop: '1rem' }}>
                  <h3 className="evrHeading3" style={{ marginBottom: '0.5rem' }}>
                    Standalone RichTextEditor:
                  </h3>
                  <RichTextEditor
                    id="standalone-rich-text-editor"
                    label="Description"
                    value={richTextValue}
                    onChange={setRichTextValue}
                    helperText="Enter a detailed description using the rich text editor"
                    charLimit={500}
                  />
                </div>
              </div>
            </div>
          </PageContent>
        </div>

        {/* SidePanel with RichTextEditor */}
        <SidePanel
          id="rich-text-sidepanel"
          open={sidePanelOpen}
          size="lg"
          ariaLabelledBy="rich-text-sidepanel-header"
          ariaDescribedBy="rich-text-sidepanel-body"
          onClose={() => setSidePanelOpen(false)}
        >
          <SidePanelHeader
            id="rich-text-sidepanel-header"
            closeButtonAriaLabel="Close RichTextEditor demo"
            onCloseButtonClick={() => setSidePanelOpen(false)}
          >
            <h3 className="evrHeading3">RichTextEditor Demo</h3>
          </SidePanelHeader>

          <SidePanelBody id="rich-text-sidepanel-body">
            <div style={{ padding: '1rem' }}>
              <RichTextEditor
                id="sidepanel-rich-text-editor"
                label="Notes"
                value={richTextValue}
                onChange={setRichTextValue}
                helperText="Add your notes here"
                charLimit={1000}
              />

              <div style={{ marginTop: '1rem' }}>
                <RichTextEditor
                  id="sidepanel-rich-text-editor-error"
                  label="Comments"
                  value=""
                  onChange={() => {}}
                  status="error"
                  statusMessage="This field is required"
                  helperText="Please provide your comments"
                  charLimit={250}
                />
              </div>
            </div>
          </SidePanelBody>

          <SidePanelFooter id="rich-text-sidepanel-footer">
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '1rem', padding: '1rem' }}>
              <Button
                id="cancel-rich-text"
                label="Cancel"
                variant="secondary"
                onClick={() => setSidePanelOpen(false)}
              />
              <Button
                id="save-rich-text"
                label="Save"
                variant="primary"
                onClick={() => {
                  console.log('Saved rich text value:', richTextValue);
                  setSidePanelOpen(false);
                }}
              />
            </div>
          </SidePanelFooter>
        </SidePanel>
      </GlobalizationProvider>
    </EverestProvider>
  );
};
