import React from 'react';
import { TNotificationBannerStatus } from '@ceridianhcm/components';
import { NotificationBanner } from '@components/NotificationBanner';
import { FullWidthWrapper } from '@components/FullWidthWrapper';
import { IEmployeeLetter } from '@/models';
import { getBannerStatus } from '../../../../utils/single-letter-utils';

export interface SingleLetterNotificationBannerProps {
  id: string;
  testId?: string;
  selectedLetter: IEmployeeLetter;
}

export const SingleLetterNotificationBanner: React.FC<SingleLetterNotificationBannerProps> = ({
  id,
  testId,
  selectedLetter,
}) => {
  const bannerInfo = getBannerStatus(selectedLetter);

  return (
    <FullWidthWrapper
      testId={`${id}-container-panel-wrapper`}
      alignItems="center"
      minWidth="880px"
      maxWidth="880px"
      fillChildren
    >
      <NotificationBanner
        id={`${id}-info-banner`}
        textMap={{ ariaLabel: `${id}-info-banner` }}
        testId={`${testId}-info-banner`}
        status={bannerInfo.status as TNotificationBannerStatus}
      >
        <NotificationBanner.Body id={`${id}-info-banner-body`}>
          <p className="evrBodyText1">{bannerInfo.message}</p>
        </NotificationBanner.Body>
      </NotificationBanner>
    </FullWidthWrapper>
  );
};
