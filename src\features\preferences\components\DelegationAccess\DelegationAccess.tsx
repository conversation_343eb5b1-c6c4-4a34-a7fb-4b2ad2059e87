import React from 'react';
import { DelegationAccessTabGroup } from './DelegationAccessTabGroup';

import '../../styles/preferences-section.scss';
import './styles.scss';

const SECTION_LABEL = 'Delegation access';
const SECTION_DESCRIPTION =
  'Give your account access to another employee for a period of time. This allows another user to perform Dayforce tasks if you will not be able to, such as when you are away.';

export const DelegationAccess: React.FC = () => {
  return (
    <div className="preferences-section-wrapper">
      <h3 className="evrHeading3">{SECTION_LABEL}</h3>
      <div className="preferences-section-content preferences-section-flex-row-override">
        <span className="evrBodyText1 preferences-section-label">{SECTION_DESCRIPTION}</span>
      </div>
      <DelegationAccessTabGroup />
    </div>
  );
};
