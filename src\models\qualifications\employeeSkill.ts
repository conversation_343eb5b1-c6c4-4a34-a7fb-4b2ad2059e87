export interface IEmployeeSkill {
  Id: number;
  SkillId: number;
  EmployeeId: number;
  SkillLevel: number;
  DateFirstAcquired: string; // ISO date string
  DateLastAcquired: string; // ISO date string
  ExpirationDate: string | null;
  AssignedByUserId: number;
  TrainingProgramId: number | null;
  CourseId: number | null;
  LastAssignedBy: string;
  LMSAssignmentMethodId: number;
  SkillAssignmentMethodCode: string;
  SkillName: string;
  SkillLevelName: string;
  HasRequiredCourses: boolean;
  EmployeeSkill: any | null;
  DaysUntilExpiration: number | null;
  IsSkillGroupActive: boolean;
  SkillExpirationThreshold: number;
  ClientEntityId: number | null;
  EntityState: number;
  LastModifiedTimestamp: string | null;
  LocalizedName: string;
  LocalizedDescription: string;
  PrimaryKeyId: number | null;
  OriginalValues: any | null;
  ExtendedProperties: any[];
}
