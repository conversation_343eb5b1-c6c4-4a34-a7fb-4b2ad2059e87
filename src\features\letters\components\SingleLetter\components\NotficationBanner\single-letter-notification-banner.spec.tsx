import React from 'react';
import { render, screen } from '@testing-library/react';
import { SingleLetterNotificationBanner } from './SingleLetterNotificationBanner';
import { getBannerStatus } from '../../../../utils/single-letter-utils';
import { IEmployeeLetter } from '@/models';

// Mock dependencies
jest.mock('../../../../utils/single-letter-utils', () => ({
  getBannerStatus: jest.fn(() => ({
    status: 'info',
    message: 'Test message',
  })),
}));

jest.mock('@ceridianhcm/components', () => ({
  NotificationBanner: ({
    children,
    testId,
    status,
  }: {
    children: React.ReactNode;
    testId?: string;
    status: string;
  }) => (
    <div data-testid={testId} data-status={status}>
      {children}
    </div>
  ),
  NotificationBannerBody: ({ children, id }: { children: React.ReactNode; id: string }) => (
    <div data-testid={id}>{children}</div>
  ),
}));

describe('SingleLetterNotificationBanner', () => {
  const mockLetter: IEmployeeLetter = {
    Subject: 'Test Letter',
    AcceptTimestamp: null,
    RejectTimestamp: null,
    ExpirationTimestamp: '2025-12-31T23:59:59Z',
    SubmittedLetterHtml: '<p>Test content</p>',
  } as IEmployeeLetter;

  const defaultProps = {
    id: 'test-banner',
    testId: 'test-banner-id',
    selectedLetter: mockLetter,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      expect(screen.getByTestId('test-banner-container-panel-wrapper')).toBeInTheDocument();
    });

    it('renders notification banner with correct props', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      const banner = screen.getByTestId('test-banner-id-info-banner');

      expect(banner).toBeInTheDocument();
      expect(banner).toHaveAttribute('data-status', 'info');
    });

    it('renders banner message from getBannerStatus', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    it('uses correct banner body id', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      expect(screen.getByTestId('test-banner-info-banner-body')).toBeInTheDocument();
    });
  });

  describe('Banner Status', () => {
    it('calls getBannerStatus with selected letter', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      expect(getBannerStatus).toHaveBeenCalledWith(mockLetter);
    });

    it('handles different banner statuses', () => {
      const statuses = ['success', 'info', 'warning', 'error'];

      statuses.forEach((status) => {
        (getBannerStatus as jest.Mock).mockReturnValueOnce({
          status,
          message: `${status} message`,
        });

        const { rerender } = render(<SingleLetterNotificationBanner {...defaultProps} />);

        const banner = screen.getByTestId('test-banner-id-info-banner');
        expect(banner).toHaveAttribute('data-status', status);
        expect(screen.getByText(`${status} message`)).toBeInTheDocument();

        rerender(<></>);
      });
    });
  });

  describe('Props Handling', () => {
    it('handles custom id and testId', () => {
      const customProps = {
        ...defaultProps,
        id: 'custom-banner',
        testId: 'custom-test-id',
      };

      render(<SingleLetterNotificationBanner {...customProps} />);

      expect(screen.getByTestId('custom-banner-container-panel-wrapper')).toBeInTheDocument();
      expect(screen.getByTestId('custom-test-id-info-banner')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles undefined banner status', () => {
      (getBannerStatus as jest.Mock).mockReturnValue({
        status: undefined,
        message: 'Test message',
      });

      render(<SingleLetterNotificationBanner {...defaultProps} />);
      expect(screen.getByTestId('test-banner-id-info-banner')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('maintains proper wrapper configuration', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      const wrapper = screen.getByTestId('test-banner-container-panel-wrapper');

      // Verify FullWidthWrapper props through class presence
      expect(wrapper).toHaveClass('full-width-container');
    });

    it('renders message with correct text class', () => {
      render(<SingleLetterNotificationBanner {...defaultProps} />);
      const message = screen.getByText('Test message');
      expect(message).toHaveClass('evrBodyText1');
    });
  });
});
