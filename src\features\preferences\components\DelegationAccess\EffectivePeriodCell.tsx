import React from 'react';
import { TableCellLayout } from '@ceridianhcm/components';
import { formatDate } from '@utils/dateUtils';

export const EffectivePeriodCell = (cellContext: any) => {
  const { EffectiveStart, EffectiveEnd } = cellContext.rowData;
  return (
    <TableCellLayout flexDirection="row" flexGap>
      <span className="cell-text-dark">{`${formatDate(EffectiveStart)} - ${formatDate(EffectiveEnd)}`}</span>
    </TableCellLayout>
  );
};
