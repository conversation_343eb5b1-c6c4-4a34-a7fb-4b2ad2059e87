# This is a basic workflow to help you get started with Actions

name: Promote MFE Standalone artifacts to GitHub Pages

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the "master" branch
  push:
    branches: ['main']

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # Build job
  build:
    if: github.actor != 'G5TFSBUILD'
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Tools
        uses: ./.github/actions/setup-tools
        with:
          npm_username: ${{ secrets.ADO_UN }}
          npm_pat: ${{ secrets.ADO_PAT_NPM }}
      - name: Build files
        run: pnpm run build-standalone

      - name: Upload static files as artifact
        id: upload-artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: dist

  # Deploy job
  deploy:
    # Add a dependency to the build job
    needs: build

    # Grant GITHUB_TOKEN the permissions required to make a Pages deployment
    permissions:
      pages: write # to deploy to Pages
      id-token: write # to verify the deployment originates from an appropriate source

    # Deploy to the github-pages environment
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    # Specify runner + deployment step
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4 # or specific "vX.X.X" version tag for this action
