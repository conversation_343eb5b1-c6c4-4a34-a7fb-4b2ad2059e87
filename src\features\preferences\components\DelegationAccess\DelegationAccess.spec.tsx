import React from 'react';
import { render, screen, waitFor, act, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

import { useStore } from '@hooks/useStore';
import { useFetchConfig } from '@hooks/useFetchConfig';
import type { AppUserDelegatesResponse } from '@models/preferences/appUserDelegates';
import { appUserDelegatesResponse } from '@mocks/preferences/appUserDelegates';
import { sanitizeId } from '@utils/sanitizeUtils';
import { DefaultTextCell } from '@components/Table/cells';
import { DelegationAccess } from './DelegationAccess';
import { NameCell } from './NameCell';
import { EffectivePeriodCell } from './EffectivePeriodCell';
import { RestrictCell } from './RestrictCell';

// Mock sanitizeId utility
jest.mock('@utils/sanitizeUtils', () => ({
  sanitizeId: jest.fn((id) => `sanitized-${id}`),
}));

// Mock hooks
jest.mock('@hooks/useStore');
jest.mock('@hooks/useFetchConfig');

jest.mock('@ceridianhcm/components', () => ({
  TableCellLayout: ({ children, flexDirection = 'row', flexGap = false, flexAlign }: any) => (
    <div
      data-testid="table-cell-layout"
      data-flex-direction={flexDirection}
      data-flex-gap={flexGap}
      data-flex-align={flexAlign}
    >
      {children}
    </div>
  ),
  Avatar: ({ ariaLabel, src, size, id }: any) => (
    <img data-testid="avatar" src={src} alt={ariaLabel} data-size={size} id={id} />
  ),
}));

jest.mock('@components/Tab', () => ({
  TabGroup: ({ children, activeId, onActiveIdChange }: any) => (
    <div data-testid="tab-group" data-active-id={activeId}>
      {React.Children.map(children, (child) =>
        React.cloneElement(child, { onSelect: () => onActiveIdChange(child.props.id) }),
      )}
    </div>
  ),
  Tab: ({ children, id, label, onSelect }: any) => (
    <div data-testid={`tab-${id}`} onClick={onSelect}>
      <span>{label}</span>
      {children}
    </div>
  ),
}));

jest.mock('@components/Table', () => ({
  Table: ({ data, ariaLabel, id }: any) => (
    <div data-testid={id} aria-label={ariaLabel}>
      {data.map((row: any, idx: number) => (
        <div key={idx} data-testid="table-row">
          <span>{row.DisplayName}</span>
          <span>
            {row.EffectiveStart} - {row.EffectiveEnd}
          </span>
          <span>{row.Reason}</span>
        </div>
      ))}
    </div>
  ),
}));

const mockUseStore = useStore as jest.MockedFunction<typeof useStore>;
const mockUseFetchConfig = useFetchConfig as jest.MockedFunction<typeof useFetchConfig>;

describe('Cell Renderers', () => {
  describe('nameCell', () => {
    const mockRowData = {
      DisplayName: 'John Doe',
      PositionName: 'Manager',
      PersonImage: '/path/to/image.jpg',
    };

    it('renders with provided image', () => {
      render(NameCell({ rowData: mockRowData }));

      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveAttribute('src', '/path/to/image.jpg');
      expect(avatar).toHaveAttribute('id', 'sanitized-John Doe-avatar');
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Manager')).toBeInTheDocument();
      expect(sanitizeId).toHaveBeenCalledWith('John Doe');
    });
  });

  describe('effectivePeriodCell', () => {
    it('formats date range correctly', () => {
      const mockRowData = {
        EffectiveStart: '2024-01-15T00:00:00',
        EffectiveEnd: '2024-12-31T23:59:00',
      };

      render(EffectivePeriodCell({ rowData: mockRowData }));
      expect(screen.getByText('Jan 15, 2024 - Dec 31, 2024')).toBeInTheDocument();
    });
  });

  describe('defaultTextCell', () => {
    it('renders value from getValue function', () => {
      render(DefaultTextCell({ getValue: () => 'Function Value' }));
      expect(screen.getByText('Function Value')).toBeInTheDocument();
    });

    it('renders direct context value', () => {
      render(DefaultTextCell('Direct Value'));
      expect(screen.getByText('Direct Value')).toBeInTheDocument();
    });
  });

  describe('restrictCell', () => {
    it('renders all restrictions', () => {
      const mockRowData = {
        RestrictPayAccessString: 'Pay',
        RestrictCompensationAccessString: 'Comp',
        RestrictPerformanceAccessString: 'Perf',
        RestrictSuccessionAccessString: 'Succ',
        RestrictPiiDocumentAccessString: 'PII',
      };

      render(RestrictCell({ rowData: mockRowData }));
      expect(screen.getByText('Pay, Comp, Perf, Succ, PII')).toBeInTheDocument();
    });

    it('handles empty restrictions', () => {
      const mockRowData = {
        RestrictPayAccessString: '',
        RestrictCompensationAccessString: '',
        RestrictPerformanceAccessString: '',
        RestrictSuccessionAccessString: '',
        RestrictPiiDocumentAccessString: '',
      };

      render(RestrictCell({ rowData: mockRowData }));
      expect(screen.getByTestId('table-cell-layout')).toHaveTextContent('');
    });

    it('handles partial restrictions', () => {
      const mockRowData = {
        RestrictPayAccessString: 'Pay',
        RestrictCompensationAccessString: '',
        RestrictPerformanceAccessString: 'Perf',
        RestrictSuccessionAccessString: '',
        RestrictPiiDocumentAccessString: '',
      };

      render(RestrictCell({ rowData: mockRowData }));
      expect(screen.getByText('Pay, Perf')).toBeInTheDocument();
    });
  });
});

describe('DelegationAccess Component', () => {
  const mockPrefStore = {
    fetchAppUserDelegates: jest.fn(),
    destroy: jest.fn(),
  } as any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseStore.mockReturnValue(mockPrefStore);
  });

  it('calls useFetchConfig with correct configuration', () => {
    mockUseFetchConfig.mockImplementation(() => {});
    render(<DelegationAccess />);

    expect(mockUseFetchConfig).toHaveBeenCalledWith({
      appUserDelegates: {
        fetch: mockPrefStore.fetchAppUserDelegates,
        onSuccess: expect.any(Function),
        errorMessage: 'Failed to fetch app user delegates',
      },
    });
  });

  it('renders delegates data after successful fetch', async () => {
    let onSuccessCallback: ((res: AppUserDelegatesResponse) => void) | null = null;
    mockUseFetchConfig.mockImplementation((config: any) => {
      onSuccessCallback = config.appUserDelegates.onSuccess as any;
    });

    render(<DelegationAccess />);

    act(() => {
      if (onSuccessCallback) {
        onSuccessCallback(appUserDelegatesResponse);
      }
    });

    await waitFor(() => {
      expect(screen.getByText('Aanya Agarwal')).toBeInTheDocument();
    });
  });

  it('handles empty delegates data', async () => {
    let onSuccessCallback: ((res: AppUserDelegatesResponse) => void) | null = null;
    mockUseFetchConfig.mockImplementation((config: any) => {
      onSuccessCallback = config.appUserDelegates.onSuccess as any;
    });

    render(<DelegationAccess />);

    act(() => {
      if (onSuccessCallback) {
        onSuccessCallback({ Active: [], History: [] });
      }
    });

    // Check Active table
    await waitFor(() => {
      expect(screen.getByLabelText('Active Delegations Table')).toBeInTheDocument();
    });

    // Switch to History tab and check History table
    const historyTab = screen.getByTestId('tab-delegations-history-tab');
    fireEvent.click(historyTab);

    await waitFor(() => {
      expect(screen.getByLabelText('Delegations History Table')).toBeInTheDocument();
    });
  });

  it('switches between Active and History tabs', async () => {
    let onSuccessCallback: ((res: AppUserDelegatesResponse) => void) | null = null;
    mockUseFetchConfig.mockImplementation((config: any) => {
      onSuccessCallback = config.appUserDelegates.onSuccess as any;
    });

    render(<DelegationAccess />);

    act(() => {
      if (onSuccessCallback) {
        onSuccessCallback(appUserDelegatesResponse);
      }
    });

    // Verify Active tab content is visible
    await waitFor(() => {
      expect(screen.getByText('Aanya Agarwal')).toBeInTheDocument();
      expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
      expect(screen.getByText('Michael Rodriguez')).toBeInTheDocument();
    });

    // Switch to History tab
    const historyTab = screen.getByTestId('tab-delegations-history-tab');
    fireEvent.click(historyTab);

    // Verify History tab content is visible
    await waitFor(() => {
      expect(screen.getByText('Emma Thompson')).toBeInTheDocument();
      expect(screen.getByText('James Wilson')).toBeInTheDocument();
      expect(screen.getByText('David Kim')).toBeInTheDocument();
      // Verify Active tab content is not visible
      expect(screen.queryByText('Aanya Agarwal')).not.toBeInTheDocument();
    });

    // Switch back to Active tab
    const activeTab = screen.getByTestId('tab-active-delegations-tab');
    fireEvent.click(activeTab);

    // Verify Active tab content is visible again
    await waitFor(() => {
      expect(screen.getByText('Aanya Agarwal')).toBeInTheDocument();
      expect(screen.queryByText('Emma Thompson')).not.toBeInTheDocument();
    });
  });

  it('displays the section label and description', () => {
    render(<DelegationAccess />);

    expect(screen.getByText('Delegation access')).toBeInTheDocument();
    expect(screen.getByText(/Give your account access to another employee/)).toBeInTheDocument();
  });

  it('renders tables with correct aria labels', async () => {
    let onSuccessCallback: ((res: AppUserDelegatesResponse) => void) | null = null;
    mockUseFetchConfig.mockImplementation((config: any) => {
      onSuccessCallback = config.appUserDelegates.onSuccess as any;
    });

    render(<DelegationAccess />);

    act(() => {
      if (onSuccessCallback) {
        onSuccessCallback(appUserDelegatesResponse);
      }
    });

    // Check Active table
    expect(screen.getByLabelText('Active Delegations Table')).toBeInTheDocument();

    // Switch to History tab and check History table
    const historyTab = screen.getByTestId('tab-delegations-history-tab');
    fireEvent.click(historyTab);

    expect(screen.getByLabelText('Delegations History Table')).toBeInTheDocument();
  });

  it('handles null delegates data', async () => {
    let onSuccessCallback: ((res: AppUserDelegatesResponse) => void) | null = null;
    mockUseFetchConfig.mockImplementation((config: any) => {
      onSuccessCallback = config.appUserDelegates.onSuccess as any;
    });

    render(<DelegationAccess />);

    act(() => {
      if (onSuccessCallback) {
        onSuccessCallback(null as any);
      }
    });

    await waitFor(() => {
      expect(screen.getByLabelText('Active Delegations Table')).toBeInTheDocument();
      expect(screen.getByTestId('tab-delegations-history-tab')).toBeInTheDocument();
    });
  });
});
