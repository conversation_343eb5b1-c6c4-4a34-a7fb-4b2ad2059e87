import { IEmployeeSkill } from '@models/qualifications';

export const EmployeeSkillsMockData: IEmployeeSkill[] = [
  {
    Id: 1,
    SkillId: 5,
    EmployeeId: 1046,
    SkillLevel: 3,
    DateFirstAcquired: '2019-10-15T00:00:00',
    DateLastAcquired: '2019-10-15T00:00:00',
    ExpirationDate: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: '<PERSON> Heinerman',
    LMSAssignmentMethodId: 7,
    SkillAssignmentMethodCode: 'U',
    SkillName: 'Customer Service',
    SkillLevelName: 'Expert',
    HasRequiredCourses: false,
    EmployeeSkill: null,
    DaysUntilExpiration: null,
    IsSkillGroupActive: false,
    SkillExpirationThreshold: 7,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 2,
    SkillId: 8,
    EmployeeId: 1046,
    SkillLevel: 3,
    DateFirstAcquired: '2019-03-01T00:00:00',
    DateLastAcquired: '2019-03-01T00:00:00',
    ExpirationDate: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    SkillAssignmentMethodCode: 'U',
    SkillName: 'Data Collection and Reporting',
    SkillLevelName: 'Expert',
    HasRequiredCourses: false,
    EmployeeSkill: null,
    DaysUntilExpiration: null,
    IsSkillGroupActive: false,
    SkillExpirationThreshold: 7,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 3,
    SkillId: 4,
    EmployeeId: 1046,
    SkillLevel: 2,
    DateFirstAcquired: '2020-05-30T00:00:00',
    DateLastAcquired: '2020-05-30T00:00:00',
    ExpirationDate: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    SkillAssignmentMethodCode: 'U',
    SkillName: 'Leadership',
    SkillLevelName: 'Intermediate',
    HasRequiredCourses: false,
    EmployeeSkill: null,
    DaysUntilExpiration: null,
    IsSkillGroupActive: false,
    SkillExpirationThreshold: 7,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 4,
    SkillId: 6,
    EmployeeId: 1046,
    SkillLevel: 3,
    DateFirstAcquired: '2021-07-30T00:00:00',
    DateLastAcquired: '2021-07-30T00:00:00',
    ExpirationDate: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    SkillAssignmentMethodCode: 'U',
    SkillName: 'Teamwork',
    SkillLevelName: 'Expert',
    HasRequiredCourses: false,
    EmployeeSkill: null,
    DaysUntilExpiration: null,
    IsSkillGroupActive: false,
    SkillExpirationThreshold: 7,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
  {
    Id: 5,
    SkillId: 12,
    EmployeeId: 1046,
    SkillLevel: 3,
    DateFirstAcquired: '2019-01-01T00:00:00',
    DateLastAcquired: '2019-01-01T00:00:00',
    ExpirationDate: null,
    AssignedByUserId: 1025,
    TrainingProgramId: null,
    CourseId: null,
    LastAssignedBy: 'Abby Heinerman',
    LMSAssignmentMethodId: 7,
    SkillAssignmentMethodCode: 'U',
    SkillName: 'Technical Research',
    SkillLevelName: 'Expert',
    HasRequiredCourses: false,
    EmployeeSkill: null,
    DaysUntilExpiration: null,
    IsSkillGroupActive: false,
    SkillExpirationThreshold: 7,
    ClientEntityId: null,
    EntityState: 0,
    LastModifiedTimestamp: null,
    LocalizedName: '',
    LocalizedDescription: '',
    PrimaryKeyId: null,
    OriginalValues: null,
    ExtendedProperties: [],
  },
];
