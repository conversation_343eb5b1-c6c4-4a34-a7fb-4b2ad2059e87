import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { RichTextEditor, RichTextEditorProps } from './RichTextEditor';

// Mock Everest Community components
jest.mock('@ceridianhcm/everest-community', () => ({
  RichTextEditor: (() => {
    const MockRichTextEditor = React.forwardRef<
      HTMLTextAreaElement,
      {
        id: string;
        value?: string;
        onChange?: (value: string) => void;
        disabled?: boolean;
        readOnly?: boolean;
        required?: boolean;
        name?: string;
        'aria-label'?: string;
        'data-testid'?: string;
        'data-variant'?: string;
      }
    >(
      (
        {
          id,
          value,
          onChange,
          disabled,
          readOnly,
          required,
          name,
          'aria-label': ariaLabel,
          'data-testid': dataTestId,
          'data-variant': dataVariant,
        },
        ref,
      ) => {
        // Handle the onChange to properly simulate Everest Community RichTextEditor behavior
        const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
          onChange?.(event.target.value);
        };

        return (
          <textarea
            ref={ref}
            id={id}
            value={value || ''}
            onChange={handleChange}
            disabled={disabled}
            readOnly={readOnly}
            required={required}
            name={name}
            aria-label={ariaLabel}
            data-testid={dataTestId || `${id}-rich-text-editor`}
            data-variant={dataVariant}
          />
        );
      },
    );
    MockRichTextEditor.displayName = 'MockRichTextEditor';
    return MockRichTextEditor;
  })(),
}));

// Default props for testing
const defaultProps: RichTextEditorProps = {
  id: 'test-rich-text-editor',
  label: 'Test Rich Text Editor',
  value: '',
  onChange: jest.fn(),
};

describe('RichTextEditor', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with required props', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} />);

      // Assert
      expect(screen.getByText('Test Rich Text Editor')).toBeInTheDocument(); // Label is now rendered separately
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should render with all optional props', () => {
      // Arrange
      const fullProps: RichTextEditorProps = {
        ...defaultProps,
        value: 'test content',
        disabled: false,
        status: 'error',
        statusMessage: 'Error message',
        helperText: 'Helper text',
        charLimit: 500,
        className: 'custom-class',
      };

      // Act
      render(<RichTextEditor {...fullProps} />);

      // Assert
      const textarea = screen.getByRole('textbox');
      expect(textarea).toBeInTheDocument();
      expect(textarea).toHaveValue('test content');
      expect(screen.getByText('Test Rich Text Editor')).toBeInTheDocument(); // Label
      expect(screen.getByText('Helper text')).toBeInTheDocument(); // Helper text
      expect(screen.getByText('Error message')).toBeInTheDocument(); // Error message
      expect(screen.getByText('12/500')).toBeInTheDocument(); // Character counter
    });

    it('should render with custom className', () => {
      // Arrange & Act
      const { container } = render(<RichTextEditor {...defaultProps} className="custom-wrapper" />);

      // Assert
      expect(container.querySelector('.rich-text-editor.custom-wrapper')).toBeInTheDocument();
    });

    it('should render without className when not provided', () => {
      // Arrange & Act
      const { container } = render(<RichTextEditor {...defaultProps} />);

      // Assert
      expect(container.querySelector('.rich-text-editor')).toBeInTheDocument();
      expect(container.querySelector('.rich-text-editor')?.className).toBe('rich-text-editor');
    });

    it('should apply disabled CSS class when disabled', () => {
      // Arrange & Act
      const { container } = render(<RichTextEditor {...defaultProps} disabled />);

      // Assert
      expect(container.querySelector('.rich-text-editor--disabled')).toBeInTheDocument();
    });

    it('should apply error CSS class when status is error', () => {
      // Arrange & Act
      const { container } = render(<RichTextEditor {...defaultProps} status="error" />);

      // Assert
      expect(container.querySelector('.rich-text-editor--error')).toBeInTheDocument();
    });

    it('should apply multiple CSS classes when multiple states are active', () => {
      // Arrange & Act
      const { container } = render(
        <RichTextEditor {...defaultProps} disabled status="error" className="custom-class" />,
      );

      // Assert
      const element = container.querySelector('.rich-text-editor');
      expect(element).toHaveClass('rich-text-editor');
      expect(element).toHaveClass('rich-text-editor--disabled');
      expect(element).toHaveClass('rich-text-editor--error');
      expect(element).toHaveClass('custom-class');
    });
  });

  describe('Event Handling', () => {
    it('should call onChange when textarea value changes', async () => {
      // Arrange
      const mockOnChange = jest.fn();
      const user = userEvent.setup();

      render(<RichTextEditor {...defaultProps} onChange={mockOnChange} />);

      // Act
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'a');

      // Assert
      expect(mockOnChange).toHaveBeenCalledTimes(1);
      expect(mockOnChange).toHaveBeenCalledWith('a');
    });

    it('should call onChange for each character when typing multiple characters', async () => {
      // Arrange
      const mockOnChange = jest.fn();
      const user = userEvent.setup();

      render(<RichTextEditor {...defaultProps} onChange={mockOnChange} />);

      // Act
      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'test');

      // Assert
      expect(mockOnChange).toHaveBeenCalledTimes(4);
      expect(mockOnChange).toHaveBeenNthCalledWith(1, 't');
      expect(mockOnChange).toHaveBeenNthCalledWith(2, 'te');
      expect(mockOnChange).toHaveBeenNthCalledWith(3, 'tes');
      expect(mockOnChange).toHaveBeenNthCalledWith(4, 'test');
    });
  });

  describe('Props Handling', () => {
    it('should handle disabled state', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} disabled />);

      // Assert
      expect(screen.getByRole('textbox')).toBeDisabled();
    });

    it('should handle readOnly state', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} readOnly />);

      // Assert
      expect(screen.getByRole('textbox')).toHaveAttribute('readonly');
    });

    it('should handle required state', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} required />);

      // Assert
      expect(screen.getByRole('textbox')).toBeRequired();
    });

    it('should handle error status with message', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} status="error" statusMessage="This is an error" testId="error-field" />);

      // Assert
      expect(screen.getByRole('alert')).toBeInTheDocument();
      expect(screen.getByText('This is an error')).toBeInTheDocument();
    });

    it('should handle helper text', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} helperText="This is helper text" testId="helper-field" />);

      // Assert
      expect(screen.getByText('This is helper text')).toBeInTheDocument();
    });

    it('should handle variant prop', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} variant="smart" />);

      // Assert
      expect(screen.getByRole('textbox')).toHaveAttribute('data-variant', 'smart');
    });
  });

  describe('Character Limit Functionality', () => {
    it('should render character counter when charLimit is provided', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} charLimit={100} value="Hello" testId="char-limit-field" />);

      // Assert
      expect(screen.getByText('5/100')).toBeInTheDocument();
    });

    it('should use charLimitAltText when provided', () => {
      // Arrange & Act
      render(
        <RichTextEditor
          {...defaultProps}
          charLimit={200}
          charLimitAltText="5 out of 200 characters used"
          value="Hello"
          testId="alt-text-field"
        />,
      );

      // Assert
      expect(screen.getByText('5 out of 200 characters used')).toBeInTheDocument();
    });

    it('should not render character counter when charLimit is not provided', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} value="Hello" testId="no-limit-field" />);

      // Assert
      expect(screen.queryByText(/\/\d+$/)).not.toBeInTheDocument(); // No counter pattern like "5/100"
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} ariaLabel="Custom aria label" />);

      // Assert
      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveAttribute('aria-label', 'Custom aria label');
    });

    it('should render label text when provided', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} />);

      // Assert
      expect(screen.getByText('Test Rich Text Editor')).toBeInTheDocument();
    });

    it('should have proper name attribute', () => {
      // Arrange & Act
      render(<RichTextEditor {...defaultProps} name="test-name" />);

      // Assert
      expect(screen.getByRole('textbox')).toHaveAttribute('name', 'test-name');
    });
  });

  describe('Forward Ref', () => {
    it('should forward ref to the textarea element', () => {
      // Arrange
      const ref = React.createRef<HTMLTextAreaElement>();

      // Act
      render(<RichTextEditor {...defaultProps} ref={ref} />);

      // Assert
      expect(ref.current).toBeInstanceOf(HTMLTextAreaElement);
      expect(ref.current?.id).toBe('test-rich-text-editor');
    });
  });
});
