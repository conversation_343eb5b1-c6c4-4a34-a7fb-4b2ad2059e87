import { ICertificationGroupByStatus } from '@models/qualifications';

export const CertificationGroupByStatusMockData: ICertificationGroupByStatus[] = [
  {
    LMSCertificationId: 7302,
    LMSEmployeeCertificationId: 7677,
    EmployeeId: 3658,
    SubmissionDate: '2025-05-22T07:39:41.227',
    SubmittedByUserId: 3658,
    SubmittedBy: 'Co<PERSON> <PERSON>',
    CertificationShortName: 'DDS Certification 1',
    LMSCertificationStatus: 'Pending Approval',
    LMSCertificationStatusId: 1,
    LMSCertificationStatusXRefCode: 'PENDING_APPROVAL',
    ClientId: 300016,
    DFWorkflowDataId: 1922,
  },
  {
    LMSCertificationId: 7302,
    LMSEmployeeCertificationId: 7677,
    EmployeeId: 3658,
    SubmissionDate: '2025-05-22T07:39:41.227',
    SubmittedByUserId: 3658,
    SubmittedBy: 'Co<PERSON> <PERSON>',
    CertificationShortName: 'DDS Certification 2',
    LMSCertificationStatus: 'Rejected',
    LMSCertificationStatusId: 1,
    LMSCertificationStatusXRefCode: 'REJECTED',
    ClientId: 300016,
    DFWorkflowDataId: 1922,
  },
];
