import React from 'react';
import { render, screen } from '@testing-library/react';
import { FullWidthWrapper } from './FullWidthWrapper';

describe('FullWidthWrapper', () => {
  const defaultProps = {
    testId: 'test-wrapper',
    children: <div>Test Content</div>,
  };

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(<FullWidthWrapper {...defaultProps} />);
      expect(screen.getByTestId('test-wrapper')).toBeInTheDocument();
    });

    it('renders children correctly', () => {
      render(<FullWidthWrapper {...defaultProps} />);
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<FullWidthWrapper {...defaultProps} className="custom-class" />);
      const wrapper = screen.getByTestId('test-wrapper');
      expect(wrapper).toHaveClass('custom-class', 'full-width-container');
    });
  });

  describe('Width Handling', () => {
    const presetWidths: Array<'default' | 'full' | 'unset'> = ['default', 'full', 'unset'];

    it('handles preset min width values', () => {
      presetWidths.forEach((width) => {
        const { rerender } = render(<FullWidthWrapper {...defaultProps} minWidth={width} />);
        const wrapper = screen.getByTestId('test-wrapper');

        if (width !== 'default') {
          expect(wrapper).toHaveStyle({ minWidth: width === 'unset' ? 'unset' : undefined });
        }

        rerender(<></>);
      });
    });

    it('handles preset max width values', () => {
      presetWidths.forEach((width) => {
        const { rerender } = render(<FullWidthWrapper {...defaultProps} maxWidth={width} />);
        const wrapper = screen.getByTestId('test-wrapper');

        if (width !== 'default') {
          expect(wrapper).toHaveStyle({ maxWidth: width === 'unset' ? 'unset' : undefined });
        }

        rerender(<></>);
      });
    });

    it('handles custom min width values', () => {
      render(<FullWidthWrapper {...defaultProps} minWidth="720px" />);
      expect(screen.getByTestId('test-wrapper')).toHaveStyle({ minWidth: '720px' });
    });

    it('handles custom max width values', () => {
      render(<FullWidthWrapper {...defaultProps} maxWidth="1440px" />);
      expect(screen.getByTestId('test-wrapper')).toHaveStyle({ maxWidth: '1440px' });
    });
  });

  describe('Alignment Handling', () => {
    const alignments: Array<'start' | 'center' | 'end' | 'stretch' | 'baseline'> = [
      'start',
      'center',
      'end',
      'stretch',
      'baseline',
    ];

    alignments.forEach((alignment) => {
      it(`applies correct class for ${alignment} alignment`, () => {
        render(<FullWidthWrapper {...defaultProps} alignItems={alignment} />);
        expect(screen.getByTestId('test-wrapper')).toHaveClass(`align-${alignment}`);
      });
    });
  });

  describe('Padding Handling', () => {
    const presetPaddings: Array<'none' | 'sm' | 'md' | 'l' | 'xl' | '2xl'> = ['none', 'sm', 'md', 'l', 'xl', '2xl'];

    it('handles vertical padding presets', () => {
      presetPaddings.forEach((padding) => {
        const { rerender } = render(<FullWidthWrapper {...defaultProps} verticalPadding={padding} />);
        const wrapper = screen.getByTestId('test-wrapper');
        const expectedValue = padding === 'none' ? '0' : `var(--evr-spacing-${padding})`;
        expect(wrapper).toHaveStyle({
          paddingTop: expectedValue,
          paddingBottom: expectedValue,
        });
        rerender(<></>);
      });
    });

    it('handles horizontal padding presets', () => {
      presetPaddings.forEach((padding) => {
        const { rerender } = render(<FullWidthWrapper {...defaultProps} horizontalPadding={padding} />);
        const wrapper = screen.getByTestId('test-wrapper');
        const expectedValue = padding === 'none' ? '0' : `var(--evr-spacing-${padding})`;
        expect(wrapper).toHaveStyle({
          paddingLeft: expectedValue,
          paddingRight: expectedValue,
        });
        rerender(<></>);
      });
    });

    it('handles custom vertical padding values', () => {
      render(<FullWidthWrapper {...defaultProps} verticalPadding="16px" />);
      expect(screen.getByTestId('test-wrapper')).toHaveStyle({
        paddingTop: '16px',
        paddingBottom: '16px',
      });
    });

    it('handles custom horizontal padding values', () => {
      render(<FullWidthWrapper {...defaultProps} horizontalPadding="24px" />);
      expect(screen.getByTestId('test-wrapper')).toHaveStyle({
        paddingLeft: '24px',
        paddingRight: '24px',
      });
    });
  });

  describe('Fill Children Handling', () => {
    it('applies fill-children class when enabled', () => {
      render(<FullWidthWrapper {...defaultProps} fillChildren />);
      expect(screen.getByTestId('test-wrapper')).toHaveClass('fill-children');
    });

    it('does not apply fill-children class when disabled', () => {
      render(<FullWidthWrapper {...defaultProps} fillChildren={false} />);
      expect(screen.getByTestId('test-wrapper')).not.toHaveClass('fill-children');
    });
  });

  describe('Edge Cases', () => {
    it('handles all default props', () => {
      render(<FullWidthWrapper>{defaultProps.children}</FullWidthWrapper>);
      const wrapper = screen.getByText('Test Content').parentElement;
      expect(wrapper).toHaveClass('full-width-container');
    });

    it('handles undefined className', () => {
      render(<FullWidthWrapper {...defaultProps} className={undefined} />);
      expect(screen.getByTestId('test-wrapper')).toHaveClass('full-width-container');
    });

    it('handles invalid padding values gracefully', () => {
      render(<FullWidthWrapper {...defaultProps} verticalPadding="invalid" horizontalPadding="invalid" />);
      const wrapper = screen.getByTestId('test-wrapper');
      expect(wrapper).toHaveStyle({
        paddingTop: 'invalid',
        paddingBottom: 'invalid',
        paddingLeft: 'invalid',
        paddingRight: 'invalid',
      });
    });
  });
});
