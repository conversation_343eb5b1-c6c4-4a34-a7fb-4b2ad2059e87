import { IEmployeeCertificationNoteResponse } from '@models/qualifications';

export const EmployeeCertificationsNoteMockData: IEmployeeCertificationNoteResponse = {
  Success: true,
  ErrorMessage: null,
  EntityLists: [
    {
      Total: null,
      Type: 'Dayforce.Web.HR.ViewModels.Profile.Learning.LMSEmployeeCertificationNoteViewModel, Dayforce.Web.HR, Version=70.0.0.14213, Culture=neutral, PublicKeyToken=null',
      Entities: [
        {
          LMSEmployeeCertificationNoteId: 111,
          LMSEmployeeCertificationId: 7677,
          Attachments: [
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 111,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 665,
              FileName: 'MATH X.pdf',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 111,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 666,
              FileName: 'MathsBasic-SQP.pdf',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 111,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 667,
              FileName: 'MathsStandard-SQP.pdf',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
          ],
          EmployeeId: 3658,
          Title: null,
          Comment: 'dsd',
          CreatedDate: '2025-05-22T07:39:40.947',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:39",
          CreatedUserDisplayName: 'George Costanza',
          CreatedUserId: 3658,
          CanViewCreatedUser: false,
          LastModifiedUserId: 3658,
          LastModifiedDisplayName: 'George Costanza',
          CanViewLastModifiedUser: false,
          LastModifiedDate: '2025-05-22T07:39:40.987',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:39",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSEmployeeCertificationNoteId: 109,
          LMSEmployeeCertificationId: 7675,
          Attachments: [
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 109,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 660,
              FileName: 'MathsStandard-SQP.pdf',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
          ],
          EmployeeId: 3658,
          Title: '',
          Comment: 'Sample questionnaire',
          CreatedDate: '2025-05-22T07:29:48.283',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:29",
          CreatedUserDisplayName: 'Macon Burke',
          CreatedUserId: 1095,
          CanViewCreatedUser: true,
          LastModifiedUserId: 1095,
          LastModifiedDisplayName: 'Macon Burke',
          CanViewLastModifiedUser: true,
          LastModifiedDate: '2025-05-22T07:29:48.363',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:29",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSEmployeeCertificationNoteId: 108,
          LMSEmployeeCertificationId: 7674,
          Attachments: [
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 108,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 658,
              FileName: 'RuntimeBasicCalls_SCORM20043rdEdition.zip',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 108,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 659,
              FileName: 'Users_BulkUpdate.png',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
          ],
          EmployeeId: 3658,
          Title: 'RuntimeBasicCalls_SCORM20043rdEdition',
          Comment: 'scorm f',
          CreatedDate: '2025-05-22T07:26:56.747',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:26",
          CreatedUserDisplayName: 'Macon Burke',
          CreatedUserId: 1095,
          CanViewCreatedUser: true,
          LastModifiedUserId: 1095,
          LastModifiedDisplayName: 'Macon Burke',
          CanViewLastModifiedUser: true,
          LastModifiedDate: '2025-05-22T07:28:09.133',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:28",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSEmployeeCertificationNoteId: 107,
          LMSEmployeeCertificationId: 7674,
          Attachments: [
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 107,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 657,
              FileName: 'LINQ.txt',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
          ],
          EmployeeId: 3658,
          Title: '',
          Comment: 'LINQ Notes 2',
          CreatedDate: '2025-05-22T07:26:25.45',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:26",
          CreatedUserDisplayName: 'Macon Burke',
          CreatedUserId: 1095,
          CanViewCreatedUser: true,
          LastModifiedUserId: 1095,
          LastModifiedDisplayName: 'Macon Burke',
          CanViewLastModifiedUser: true,
          LastModifiedDate: '2025-05-22T07:26:25.463',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:26",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSEmployeeCertificationNoteId: 106,
          LMSEmployeeCertificationId: 7674,
          Attachments: null,
          EmployeeId: 3658,
          Title: 'LINQ Notes 1',
          Comment: 'LINQ Notes Description',
          CreatedDate: '2025-05-22T07:24:47.193',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:24",
          CreatedUserDisplayName: 'Macon Burke',
          CreatedUserId: 1095,
          CanViewCreatedUser: true,
          LastModifiedUserId: 1095,
          LastModifiedDisplayName: 'Macon Burke',
          CanViewLastModifiedUser: true,
          LastModifiedDate: '2025-05-22T07:24:47.2',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:24",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
        {
          LMSEmployeeCertificationNoteId: 105,
          LMSEmployeeCertificationId: 7672,
          Attachments: [
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 105,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 655,
              FileName: 'eloomi_new.png',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
            {
              Id: null,
              LMSEmployeeCertificationNoteId: 105,
              AttachedEntityId: null,
              AttachedEmployeeId: 3658,
              DocMgmtFileStoreId: 656,
              FileName: 'Users_BulkUpdate.png',
              FileType: null,
              FileTypeId: 37,
              FileTypeXrefCode: 'LEARNING',
              LastModifiedUserId: null,
              EntityState: 0,
            },
          ],
          EmployeeId: 3658,
          Title: 'ACR Cert 19 notes title1',
          Comment: 'ACR Cert 19 notes description',
          CreatedDate: '2025-05-22T07:21:28.61',
          CreatedTimestampString: "Today <span class='grey-text'>at</span> 07:21",
          CreatedUserDisplayName: 'Macon Burke',
          CreatedUserId: 1095,
          CanViewCreatedUser: true,
          LastModifiedUserId: 1095,
          LastModifiedDisplayName: 'Macon Burke',
          CanViewLastModifiedUser: true,
          LastModifiedDate: '2025-05-22T07:23:09.203',
          LastModifiedTimestampString: "Today <span class='grey-text'>at</span> 07:23",
          ClientEntityId: null,
          EntityState: 0,
          LastModifiedTimestamp: null,
          LocalizedName: '',
          LocalizedDescription: '',
          PrimaryKeyId: null,
          OriginalValues: null,
          ExtendedProperties: [],
        },
      ],
    },
  ],
};
