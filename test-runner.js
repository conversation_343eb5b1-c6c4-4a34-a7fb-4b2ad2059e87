// Simple test runner to check if the RichTextEditor test works
const { execSync } = require('child_process');

try {
  console.log('Running RichTextEditor test...');
  const result = execSync('npx jest src/components/RichTextEditor/rich-text-editor.spec.tsx --verbose', {
    cwd: process.cwd(),
    encoding: 'utf8',
    stdio: 'pipe'
  });
  console.log('Test output:', result);
} catch (error) {
  console.error('Test failed:', error.message);
  console.error('Error output:', error.stdout);
  console.error('Error stderr:', error.stderr);
}
