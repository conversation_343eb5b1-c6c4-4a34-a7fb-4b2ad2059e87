import { TEmployeeIdParam, TCertificationStatusParam } from '@typings/api';
import { EmployeeLetterMockData } from '@mocks/employee';
import {
  EmployeeCertificationsMockData,
  EmployeeCertificationsNoteMockData,
  EmployeeSkillsMockData,
  CertificationGroupByStatusMockData,
} from '../mocks';
import {
  getEmployeeCertifications,
  getEmployeeCertificationNotes,
  getEmployeeSkills,
  getEmployeeLetters,
  getEmployeeCertificationsGroupByStatus,
} from '../features/qualifications/api/qualificationsApi';
import { handleApiRequest } from './baseApi';
import { API_ENDPOINTS } from './apiConfig';

// Mock the handleApiRequest function
jest.mock('./baseApi', () => ({
  handleApiRequest: jest.fn(),
}));

// Mock API_ENDPOINTS
jest.mock('./apiConfig', () => ({
  API_ENDPOINTS: {
    QUALIFICATIONS_CERTIFICATIONS: '/api/qualifications/certifications',
    QUALIFICATIONS_CERTIFICATIONS_GROUP_BY_STATUS: '/api/qualifications/certifications/group-by-status',
    QUALIFICATIONS_NOTES_MODEL: '/api/qualifications/notes',
    QUALIFICATIONS_SKILLS: '/api/qualifications/skills',
    EMPLOYEE_LETTERS: '/api/employee/letters',
  },
}));

const mockHandleApiRequest = handleApiRequest as jest.MockedFunction<typeof handleApiRequest>;

describe('Qualifications API', () => {
  const mockEmployeeIdPayload: TEmployeeIdParam = { employeeId: 12345 };
  const mockStatusPayload: TCertificationStatusParam = {
    certificationStatusXrefCode: 'PENDING_APPROVAL',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getEmployeeCertifications', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse = EmployeeCertificationsMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertifications(mockEmployeeIdPayload);

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.QUALIFICATIONS_CERTIFICATIONS,
        EmployeeCertificationsMockData,
        'Failed to fetch employee certifications.',
        mockEmployeeIdPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API request failure', async () => {
      const mockError = new Error('Network error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getEmployeeCertifications(mockEmployeeIdPayload)).rejects.toThrow('Network error');
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = undefined; // Simulate unexpected response
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertifications(mockEmployeeIdPayload);

      expect(result).toBeUndefined();
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = null; // Simulate unexpected response
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertifications(mockEmployeeIdPayload);

      expect(result).toBeNull();
    });
  });

  describe('getEmployeeCertificationNotes', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse = EmployeeCertificationsNoteMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertificationNotes(mockEmployeeIdPayload);

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.QUALIFICATIONS_NOTES_MODEL,
        EmployeeCertificationsNoteMockData,
        'Failed to fetch employee certification notes.',
        mockEmployeeIdPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API request failure', async () => {
      const mockError = new Error('Server error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getEmployeeCertificationNotes(mockEmployeeIdPayload)).rejects.toThrow('Server error');
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = undefined; // Simulate unexpected response
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertificationNotes(mockEmployeeIdPayload);

      expect(result).toBeUndefined();
    });
  });

  describe('getEmployeeSkills', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse = EmployeeSkillsMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeSkills(mockEmployeeIdPayload);

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.QUALIFICATIONS_SKILLS,
        EmployeeSkillsMockData,
        'Failed to fetch employee skills.',
        mockEmployeeIdPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API request failure', async () => {
      const mockError = new Error('Network error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getEmployeeSkills(mockEmployeeIdPayload)).rejects.toThrow('Network error');
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = undefined; // Simulate unexpected response
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeSkills(mockEmployeeIdPayload);

      expect(result).toBeUndefined();
    });
  });

  describe('getEmployeeLetters', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse = EmployeeLetterMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeLetters(mockEmployeeIdPayload);

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.EMPLOYEE_LETTERS,
        EmployeeLetterMockData,
        'Failed to fetch employee letters.',
        mockEmployeeIdPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API request failure', async () => {
      const mockError = new Error('Server error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getEmployeeLetters(mockEmployeeIdPayload)).rejects.toThrow('Server error');
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = null; // Simulate unexpected response
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeLetters(mockEmployeeIdPayload);

      expect(result).toBeNull();
    });
  });

  describe('getEmployeeCertificationsGroupByStatus', () => {
    it('should call handleApiRequest with correct parameters', async () => {
      const mockResponse = CertificationGroupByStatusMockData;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertificationsGroupByStatus(mockStatusPayload);

      expect(mockHandleApiRequest).toHaveBeenCalledWith(
        'POST',
        API_ENDPOINTS.QUALIFICATIONS_CERTIFICATIONS_GROUP_BY_STATUS,
        CertificationGroupByStatusMockData,
        'Failed to fetch certification group by status.',
        mockStatusPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API request failure', async () => {
      const mockError = new Error('Network error');
      mockHandleApiRequest.mockRejectedValue(mockError);

      await expect(getEmployeeCertificationsGroupByStatus(mockStatusPayload)).rejects.toThrow('Network error');
    });

    it('should handle unexpected response format', async () => {
      const mockResponse = undefined;
      mockHandleApiRequest.mockResolvedValue(mockResponse);

      const result = await getEmployeeCertificationsGroupByStatus(mockStatusPayload);
      expect(result).toBeUndefined();
    });

    it('should handle different status codes', async () => {
      const statuses: TCertificationStatusParam['certificationStatusXrefCode'][] = ['PENDING_APPROVAL', 'REJECTED'];

      // Replace for...of loop with Promise.all and map
      const results = await Promise.all(
        statuses.map((status) => {
          const payload = { ...mockStatusPayload, certificationStatusXrefCode: status };
          mockHandleApiRequest.mockResolvedValueOnce(CertificationGroupByStatusMockData);
          return getEmployeeCertificationsGroupByStatus(payload);
        }),
      );

      // Verify all results
      results.forEach((result) => {
        expect(result).toEqual(CertificationGroupByStatusMockData);
      });

      // Verify the mock was called correct number of times
      expect(mockHandleApiRequest).toHaveBeenCalledTimes(statuses.length);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle multiple concurrent API calls', async () => {
      const mockResponses = {
        certifications: EmployeeCertificationsMockData,
        notes: EmployeeCertificationsNoteMockData,
      };

      mockHandleApiRequest
        .mockResolvedValueOnce(mockResponses.certifications)
        .mockResolvedValueOnce(mockResponses.notes);

      const [certificationsResult, notesResult] = await Promise.all([
        getEmployeeCertifications(mockEmployeeIdPayload),
        getEmployeeCertificationNotes(mockEmployeeIdPayload),
      ]);

      expect(certificationsResult).toEqual(mockResponses.certifications);
      expect(notesResult).toEqual(mockResponses.notes);
      expect(mockHandleApiRequest).toHaveBeenCalledTimes(2);
    });

    it('should handle mixed success and failure scenarios', async () => {
      const mockSuccessResponse = EmployeeCertificationsMockData;
      const mockError = new Error('Network timeout');

      mockHandleApiRequest.mockResolvedValueOnce(mockSuccessResponse).mockRejectedValueOnce(mockError);

      const certificationsPromise = getEmployeeCertifications(mockEmployeeIdPayload);
      const notesPromise = getEmployeeCertificationNotes(mockEmployeeIdPayload);

      await expect(certificationsPromise).resolves.toEqual(mockSuccessResponse);
      await expect(notesPromise).rejects.toThrow('Network timeout');
    });

    it('should handle empty responses gracefully', async () => {
      mockHandleApiRequest.mockResolvedValueOnce([]).mockResolvedValueOnce([]);

      const certificationsResult = await getEmployeeCertifications(mockEmployeeIdPayload);
      const notesResult = await getEmployeeCertificationNotes(mockEmployeeIdPayload);

      expect(certificationsResult).toEqual([]);
      expect(notesResult).toEqual([]);
    });

    it('should handle multiple concurrent API calls for skills and letters', async () => {
      const mockResponses = {
        skills: EmployeeSkillsMockData,
        letters: EmployeeLetterMockData,
      };

      mockHandleApiRequest.mockResolvedValueOnce(mockResponses.skills).mockResolvedValueOnce(mockResponses.letters);

      const [skillsResult, lettersResult] = await Promise.all([
        getEmployeeSkills(mockEmployeeIdPayload),
        getEmployeeLetters(mockEmployeeIdPayload),
      ]);

      expect(skillsResult).toEqual(mockResponses.skills);
      expect(lettersResult).toEqual(mockResponses.letters);
      expect(mockHandleApiRequest).toHaveBeenCalledTimes(2);
    });

    it('should handle mixed success and failure scenarios for skills and letters', async () => {
      const mockSuccessResponse = EmployeeSkillsMockData;
      const mockError = new Error('Network timeout');

      mockHandleApiRequest.mockResolvedValueOnce(mockSuccessResponse).mockRejectedValueOnce(mockError);

      const skillsPromise = getEmployeeSkills(mockEmployeeIdPayload);
      const lettersPromise = getEmployeeLetters(mockEmployeeIdPayload);

      await expect(skillsPromise).resolves.toEqual(mockSuccessResponse);
      await expect(lettersPromise).rejects.toThrow('Network timeout');
    });

    it('should handle empty responses gracefully for skills and letters', async () => {
      mockHandleApiRequest.mockResolvedValueOnce([]).mockResolvedValueOnce([]);

      const skillsResult = await getEmployeeSkills(mockEmployeeIdPayload);
      const lettersResult = await getEmployeeLetters(mockEmployeeIdPayload);

      expect(skillsResult).toEqual([]);
      expect(lettersResult).toEqual([]);
    });

    it('should handle all qualification endpoints concurrently', async () => {
      const mockResponses = {
        certifications: EmployeeCertificationsMockData,
        groupByStatus: CertificationGroupByStatusMockData,
        notes: EmployeeCertificationsNoteMockData,
        skills: EmployeeSkillsMockData,
        letters: EmployeeLetterMockData,
      };

      mockHandleApiRequest
        .mockResolvedValueOnce(mockResponses.certifications)
        .mockResolvedValueOnce(mockResponses.groupByStatus)
        .mockResolvedValueOnce(mockResponses.notes)
        .mockResolvedValueOnce(mockResponses.skills)
        .mockResolvedValueOnce(mockResponses.letters);

      const [certificationsResult, groupByStatusResult, notesResult, skillsResult, lettersResult] = await Promise.all([
        getEmployeeCertifications(mockEmployeeIdPayload),
        getEmployeeCertificationsGroupByStatus(mockStatusPayload),
        getEmployeeCertificationNotes(mockEmployeeIdPayload),
        getEmployeeSkills(mockEmployeeIdPayload),
        getEmployeeLetters(mockEmployeeIdPayload),
      ]);

      expect(certificationsResult).toEqual(mockResponses.certifications);
      expect(groupByStatusResult).toEqual(mockResponses.groupByStatus);
      expect(notesResult).toEqual(mockResponses.notes);
      expect(skillsResult).toEqual(mockResponses.skills);
      expect(lettersResult).toEqual(mockResponses.letters);
      expect(mockHandleApiRequest).toHaveBeenCalledTimes(5);
    });

    it('should handle mixed response types', async () => {
      mockHandleApiRequest
        .mockResolvedValueOnce([]) // Empty certifications
        .mockResolvedValueOnce(null) // No group by status
        .mockResolvedValueOnce({ EntityLists: [] }); // Empty notes

      const [certificationsResult, groupByStatusResult, notesResult] = await Promise.all([
        getEmployeeCertifications(mockEmployeeIdPayload),
        getEmployeeCertificationsGroupByStatus(mockStatusPayload),
        getEmployeeCertificationNotes(mockEmployeeIdPayload),
      ]);

      expect(certificationsResult).toEqual([]);
      expect(groupByStatusResult).toBeNull();
      expect(notesResult).toEqual({ EntityLists: [] });
    });
  });
});
