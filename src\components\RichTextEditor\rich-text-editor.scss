.rich-text-editor {
  display: flex;
  flex-direction: column;
  width: 100%;

  // Spacing between rich text editors
  & + & {
    margin-top: var(--evr-spacing-md);
  }

  // Semantic spacing classes for layout flexibility
  &--spacing-xs {
    margin-bottom: var(--evr-spacing-xs);
  }

  &--spacing-sm {
    margin-bottom: var(--evr-spacing-sm);
  }

  &--spacing-md {
    margin-bottom: var(--evr-spacing-md);
  }

  &--spacing-lg {
    margin-bottom: var(--evr-spacing-lg);
  }

  &--spacing-xl {
    margin-bottom: var(--evr-spacing-xl);
  }

  // Focus state enhancements
  &:focus-within {
    // The TextArea component handles its own focus states
    // Additional focus styling can be added here if needed
  }

  // Disabled state styling
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
    cursor: not-allowed;

    .rich-text-editor__label {
      color: var(--evr-inactive-content);
    }
  }

  // Read-only state styling
  &--read-only {
    .rich-text-editor__label {
      color: var(--evr-content-secondary);
    }

    // Add visual indicator for read-only state
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--evr-inactive-surfaces);
      opacity: 0.1;
      pointer-events: none;
    }
  }

  // Error state styling
  &--error {
    .rich-text-editor__label {
      color: var(--evr-content-error);
    }

    // Add subtle error border or background
    border-left: var(--evr-border-width-thick) solid var(--evr-content-error);
    padding-left: var(--evr-spacing-xs);
  }

  // Full width variant
  &--full-width {
    width: 100%;
  }

  // Compact variant for tighter layouts
  &--compact {
    & + & {
      margin-top: var(--evr-spacing-sm);
    }
  }

  // Smart variant styling
  &--smart {
    // Smart variant has auto-growing behavior and more compact styling
    .rich-text-editor__label {
      font-size: var(--evr-font-size-xs);
      margin-bottom: var(--evr-spacing-2xs);
    }

    .rich-text-editor__helper,
    .rich-text-editor__error,
    .rich-text-editor__counter {
      font-size: var(--evr-font-size-2xs);
      margin-top: var(--evr-spacing-2xs);
    }

    // Tighter spacing for smart variant
    & + & {
      margin-top: var(--evr-spacing-sm);
    }
  }

  // Label styling
  &__label {
    margin-bottom: var(--evr-spacing-xs);
    font-weight: var(--evr-font-weight-medium);
    color: var(--evr-content-primary);
    font-size: var(--evr-font-size-sm);
  }

  // Helper text styling
  &__helper {
    margin-top: var(--evr-spacing-xs);
    font-size: var(--evr-font-size-xs);
    color: var(--evr-content-secondary);
  }

  // Error message styling
  &__error {
    margin-top: var(--evr-spacing-xs);
    font-size: var(--evr-font-size-xs);
    color: var(--evr-content-error);
  }

  // Character counter styling
  &__counter {
    margin-top: var(--evr-spacing-xs);
    font-size: var(--evr-font-size-xs);
    color: var(--evr-content-secondary);
    text-align: right;
  }

  // Rich text specific styling
  &__content {
    // Future: When a dedicated RichTextEditor component is available,
    // rich text specific styling can be added here
    // For now, the RichTextEditor component provides the base functionality
  }

  // Toolbar styling (for future rich text editor features)
  &__toolbar {
    // Future: Toolbar styling for rich text formatting options
    display: none; // Hidden until rich text editor is implemented
    padding: var(--evr-spacing-xs);
    border-bottom: var(--evr-border-width-thin) solid var(--evr-border-neutral-default);
    background-color: var(--evr-surfaces-secondary-default);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .rich-text-editor {
    // Mobile-specific adjustments
    & + & {
      margin-top: var(--evr-spacing-sm);
    }

    // Adjust spacing for mobile
    &--spacing-md {
      margin-bottom: var(--evr-spacing-sm);
    }

    &--spacing-lg {
      margin-bottom: var(--evr-spacing-md);
    }

    &--spacing-xl {
      margin-bottom: var(--evr-spacing-lg);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .rich-text-editor {
    // High contrast adjustments if needed
    // The TextArea component should handle most high contrast needs
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .rich-text-editor {
    // Disable animations/transitions for users who prefer reduced motion
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}

// Print styles
@media print {
  .rich-text-editor {
    // Ensure proper printing of rich text editors
    break-inside: avoid;

    // Reduce spacing for print
    & + & {
      margin-top: var(--evr-spacing-xs);
    }

    // Hide toolbar in print
    &__toolbar {
      display: none;
    }
  }
}
