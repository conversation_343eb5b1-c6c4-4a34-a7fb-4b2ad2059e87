# Introduction 
The hr-profile-ui microfrontend.

# Getting Started
This project was autogenerated.  To get a better understanding of Microfrontends, visit the Wiki:

- [Getting Started with Microfrontends](https://wiki.dayforce.com/x/1Q74CQ)
# Install
Using nvm, run the command below to use the NodeJS version set in .nvmrc. For nodejs updates please target long-term support versions, as outlined in this [document](https://nodejs.org/en/about/releases/):

```
nvm use
```
and then install dependencies with:
```
pnpm i
```

# Build & Watch
There are multiple ways to build the project:

## Inside Dayforce
(By pointing the Dayforce AppShell to this local build)  
If you are targeting to run the microfrontend inside Dayforce, by default **dayforce.tsx** will be the entry point.  This file is a Dojo widget that will load the main React app. Run the following:
```
pnpm run start
```
If it fails, build the project and serve it as follows:
```
pnpm run build
pnpm run serve
```

# Independant of a locally hosted Dayforce
If you need to run the microfrontend without running Dayforce locally,  **standalone.tsx** will be the entry point.  Run as standalone using:
```
pnpm run standalone
```

# Using Module Federation with the Platform-AppShell
update the `module_name` in the `webpack.mf.js` file to reflect the name this module should be exposed as.   
The platform-appshell is the default Shell for running federated modules withing Dayforce.

# Test
Under the hood, we leverage *jest* and *testing-library*.  To run all the tests, just run the following:
```
pnpm test
```
You can alternatively pass in an additional command line argument to filter which tests you want to run:  ```npm test App```.
# Pipeline
There are two azure pipelines:
* azure-pipelines-pr.yaml - This should be configured to run as part of a build policy for the master repo.  You will need to configure the following:
    * Enable unit tests
    * Enable Linting
    * Ensure the build-prod script is configured
* azure-pipelines-ci.yaml - This is run when the the master branch is merged.  You will need to configure the following:
    * Set the microfrontend variable with a name in the yaml file
    * In the pipeline itself, set the following:
        * The `major` and `minor` build variables.  Examples:  860.1, 1.2
        * the `targetBranch` build variable.  This should default to **master**
    * Enable unit tests
    * Enable Linting
    * Enable SonarQube integration
    * Ensure the build-prod script is configured

# Webpack Config
The webpack config is preconfigured with the basic settings required to build the microfrontend and it's dependencies.  There are 3 files that represent the webpack config:
- webpack.common.js - The main file that has most of the configuration.
- webpack.dev.js - The dev specific version.
- webpack.prod.js - The production specific version.

We primarily rely on webpack.common.js.

# Folder Struture 

```
.

├── .github/                     # GitHub workflows and templates
├── config/                      # Webpack and other config files
│   ├── webpack.common.js        # Shared webpack config
│   ├── webpack.dev.js           # Development webpack config
│   ├── webpack.prod.js          # Production webpack config
│   └── module-federation.js     # Module federation configuration
├── public/                      # Static assets
│   ├── index.html               # HTML template
│   └── assets/                  # Static assets like images
├── src/
│   ├── bootstrap.tsx            # Bootstrap file (for module federation)
│   ├── index.tsx                # Main entry point
│   ├── App.tsx                  # App component
│   ├── declarations.d.ts        # TypeScript declarations
│   ├── remote-types.d.ts        # Remote types for module federation
│   ├── assets/                  # Asset files (images, fonts)
│   ├── components/              # Shared/common components
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   └── Button.module.scss
│   │   └── ...
│   ├── features/                # Feature-based organization
│   │   ├── feature1/
│   │   │   ├── components/      # Feature-specific components
│   │   │   ├── hooks/           # Feature-specific hooks
│   │   │   ├── store/           # Feature-specific store
│   │   │   │   ├── index.ts
│   │   │   │   └── FeatureStore.ts
│   │   │   └── views/           # Feature views/pages
│   │   │       ├── MainView.tsx
│   │   │       └── DetailView.tsx
│   │   └── feature2/
│   │       └── ...
│   ├── store/                   # Global state management
│   │   ├── index.ts             # Store barrel file
│   │   ├── GlobalStore.ts       # Global application store
│   │   └── AppStore.ts          # App-level store
│   ├── hooks/                   # Custom hooks
│   │   ├── useApi.ts
│   │   └── ...
│   ├── services/                # Service layers
│   │   ├── api.ts               # API service
│   │   └── ...
│   ├── styles/                  # Global styles
│   │   ├── tokens/              # Design tokens
│   │   │   ├── _colors.scss
│   │   │   ├── _typography.scss
│   │   │   ├── _spacing.scss
│   │   │   └── _index.scss
│   │   ├── mixins/              # SCSS mixins
│   │   │   ├── _responsive.scss
│   │   │   └── _index.scss
│   │   ├── _variables.scss      # SCSS variables
│   │   ├── _reset.scss          # CSS reset
│   │   └── global.scss          # Global styles
│   ├── types/                   # TypeScript types
│   │   ├── common.ts
│   │   └── api.ts
│   └── utils/                   # Utility functions
│       ├── formatting.ts
│       └── validation.ts
├── .eslintrc.js                 # ESLint configuration
├── .prettierrc                  # Prettier configuration
├── tsconfig.json                # TypeScript configuration
├── package.json                 # Package manager file
└── README.md                    # Documentation
```

## JavaScript/React Naming Conventions

## Folder Naming Conventions

- Use **kebab-case** for general purpose folders: user-management/, payment-processing/
- Use **lowercase** for standard/conventional folders: components/, hooks/, utils/

#### Be consistent with plurality:
- Use singular for concept folders: store/, context/, hook/
- Use plural for collection folders: components/, utils/, types/

- Group related files in descriptive folders rather than using prefixes in filenames
Nest related folders logically while avoiding excessive nesting (aim for max 2-3 levels deep)
Organize feature folders with consistent internal structure:
```
features/
├── user-management/   # Feature name in kebab-case
│   ├── components/    # Standard folder in lowercase
│   ├── hooks/
│   ├── store/
│   └── views/
```

## React Components


### File Naming
- **PascalCase** for component files: `Button.tsx`, `UserProfile.tsx`, `NavigationMenu.tsx`
- Use descriptive, noun-based names that reflect the component's purpose
- Include the file extension: `.tsx` for TypeScript components, `.jsx` for JavaScript components
- Test files should match the component name with `.test` or `.spec` suffix: `Button.test.tsx`
- Style files should match the component name: `Button.module.scss`, `UserProfile.styles.ts`

### Component Naming
- Use **PascalCase** for React component names:
  ```jsx
  // Good
  function UserProfile() { ... }
  const NavigationBar = () => { ... }
  class SearchForm extends React.Component { ... }
  
  // Bad
  function userProfile() { ... }
  const navigationBar = () => { ... }
  ```
  
- Prefix higher-order components with `with`:
  ```jsx
  // Higher-order component
  const withAuth = (Component) => { ... }
  ```

- Use clear, descriptive names that indicate the component's purpose or functionality
- For feature-specific components, consider using the feature name as a prefix:
  ```jsx
  // Components inside the "payments" feature
  PaymentForm.tsx
  PaymentSummary.tsx
  PaymentHistory.tsx
  ```

## Utilities/Helpers

### File Naming
- Use **camelCase** for utility files: `formatCurrency.ts`, `validateInput.ts`
- Group related utilities in directory with an `index.ts` barrel file
- Use verbs for action-based utilities and nouns for utility objects/constants
  
### Function Naming
- Use **camelCase** and start with a verb:
  ```javascript
  // Good
  function formatDate(date) { ... }
  const calculateTotal = (items) => { ... }
  
  // Bad
  function DateFormat(date) { ... }
  const total_calculation = (items) => { ... }
  ```

- For boolean-returning functions, use `is`, `has`, or `should` prefix:
  ```javascript
  const isValidEmail = (email) => { ... }
  const hasPermission = (user, action) => { ... }
  const shouldDisplayNotification = (status) => { ... }
  ```

## Store Management (Platform State)

### File Naming
- Use **PascalCase** with `Store` suffix for store class files: `CounterStore.ts`, `UserProfileStore.ts`
- Use **camelCase** for non-class files related to the store: `storeUtils.ts`, `storeSelectors.ts`

### Store Naming
- Use **PascalCase** with `Store` suffix for store classes:
  ```typescript
  // Using Platform State
  class UserProfileStore {
    firstName = '';
    lastName = '';
    // ...
  }
  
  export const UserProfileStore = register(UserProfileStore, 'UserProfileStore');
  ```

### Action/Method Naming
- Use **camelCase** and start with a verb for actions that mutate state:
  ```typescript
  class ProductStore {
    items = [];
    
    addItem(item) { ... }
    removeItem(id) { ... }
    updateQuantity(id, quantity) { ... }
  }
  ```

- Use **camelCase** with appropriate prefixes for getter methods:
  ```typescript
  class CartStore {
    items = [];
    
    getTotal() { ... }
    hasItems() { ... }
    isValid() { ... }
  }
  ```

- Use underscore prefix for non-mutable actions (Platform State specific):
  ```typescript
  class FormStore {
    formData = {};
    
    // Won't trigger re-render
    _processFormData(data) { ... }
  }
  ```

## Hooks

### File Naming
- Prefix hook files with `use`: `useApi.ts`, `useDebounce.ts`
- Use **camelCase** for the file name

### Hook Naming
- Prefix hooks with `use` and use **camelCase**:
  ```javascript
  // Good
  function useWindowSize() { ... }
  const useFormValidation = (initialValues) => { ... }
  
  // Bad
  function windowSize() { ... }
  const FormValidation = (initialValues) => { ... }
  ```

- For feature-specific hooks, consider namespace prefixing:
  ```javascript
  // Authentication feature hooks
  function useAuthLogin() { ... }
  function useAuthLogout() { ... }
  ```

## Constants

- Use **UPPER_SNAKE_CASE** for true constants:
  ```javascript
  const API_BASE_URL = 'https://api.example.com';
  const MAX_RETRIES = 3;
  ```

- For enums or object constants, use **PascalCase**:
  ```typescript
  const HttpStatus = {
    OK: 200,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  };
  
  enum UserRole {
    ADMIN = 'admin',
    USER = 'user',
    GUEST = 'guest'
  }
  ```

## Feature Organization

- Use **kebab-case** for feature directory names: `user-management/`, `product-catalog/`
- Use consistent internal structure across features:
  ```
  features/
  ├── user-management/
  │   ├── components/
  │   ├── hooks/
  │   ├── store/
  │   ├── views/
  │   └── index.ts
  ```

## Imports and Exports

- Export named components/functions as default when there's only one export per file
- Use barrel files (`index.ts`) to re-export from feature directories
- Group and organize imports in a consistent order:
  1. External libraries
  2. Internal modules
  3. Component-level imports (styles, assets)

  ```javascript
  // External libraries
  import React, { useState, useEffect } from 'react';
  import { useNavigate } from 'react-router-dom';
  
  // Internal modules
  import { useSource } from '@platform/state';
  import { formatCurrency } from '../../utils/formatting';
  
  // Component-level imports
  import { Button } from '../Button';
  import styles from './ProductCard.module.scss';
  ```

## TypeScript Types/Interfaces

- Use **PascalCase** for interface and type names:
  ```typescript
  interface UserProps {
    id: string;
    name: string;
    email: string;
  }
  
  type ButtonSize = 'small' | 'medium' | 'large';
  ```

- Use `I` prefix for interfaces only if it helps distinguish from a class of the same name:
  ```typescript
  // When you have both a class and interface with similar purpose
  interface IUserStore { ... }
  class UserStore implements IUserStore { ... }
  ```

- For Props interfaces, suffix with `Props`:
  ```typescript
  interface ButtonProps {
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }
  ```

## CSS/SCSS Naming

- For CSS modules, use **camelCase** for class names in the component:
  ```jsx
  import styles from './Button.module.scss';
  
  function Button() {
    return <button className={styles.primaryButton}>Click Me</button>;
  }
  ```

- In SCSS files, use **kebab-case** for class selectors:
  ```scss
  .primary-button {
    background-color: $primary-color;
    
    &:hover {
      background-color: darken($primary-color, 10%);
    }
    
    &--disabled {
      opacity: 0.5;
    }
  }
  ```

- For SCSS variables, use **kebab-case**:
  ```scss
  $primary-color: #0070f3;
  $font-size-large: 18px;
  ```

## Module Federation Naming

- Use **camelCase** for the microfrontend name in module federation config:
  ```javascript
  module.exports = {
    name: 'userManagement',
    filename: 'remoteEntry.js',
    // ...
  }
  ```

- Use descriptive, clear names for exposed modules:
  ```javascript
  module.exports = {
    // ...
    exposes: {
      './UserManagement': './src/features/user-management/views/UserManagementView',
      './UserProfile': './src/features/user-management/components/UserProfile',
    }
  }

## Entry Point
The entry point is configured with the following:
```
main: webpackConfigEnv.outside
        ? path.resolve(process.cwd(), 'src/standalone.tsx')
        : path.resolve(process.cwd(), 'src/dayforce.tsx'),
```
This specifies a single entry point - one that will *work* outside of dayforce (**standalone.tsx**), and one that will work inside Dayforce (**main.tsx**).  Depending on your project specifications, you may need multiple entry files.  Note that when we use multiple entry points, the *React context will not be shared*.
# Contribute
This project is innersourced.  If you have any suggestions on how to improve the starter, feel free to create a pull request to the changes.

