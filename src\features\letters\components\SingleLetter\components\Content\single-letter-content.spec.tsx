/* eslint-disable react/display-name */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { SingleLetterContent } from './SingleLetterContent';

// Mock all external components simply
jest.mock('@ceridianhcm/components', () => ({
  Image: ({ id, alt }: { id: string; alt: string }) => <img data-testid={id} alt={alt} />,
  SectionPanel: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SectionPanelRow: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

jest.mock('@components', () => {
  const Container = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  Container.Body = ({ children, testId }: { children: React.ReactNode; testId: string }) => (
    <div data-testid={testId}>{children}</div>
  );
  return { Container };
});

jest.mock('@components/FullWidthWrapper', () => ({
  FullWidthWrapper: ({ children, testId }: { children: React.ReactNode; testId?: string }) => (
    <div data-testid={testId}>{children}</div>
  ),
}));

describe('SingleLetterContent', () => {
  const defaultProps = {
    id: 'test-content',
    testId: 'test-content-id',
    sanitizedHtml: '<p>Test content</p>',
  };

  describe('Basic Rendering', () => {
    it('renders the company logo', () => {
      render(<SingleLetterContent {...defaultProps} />);
      const logo = screen.getByTestId('dayforce-logo');
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('alt', 'dayforce logo');
    });

    it('renders the letter content', () => {
      render(<SingleLetterContent {...defaultProps} />);
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content).toBeInTheDocument();
      expect(content.innerHTML).toBe(defaultProps.sanitizedHtml);
    });

    it('renders with empty content', () => {
      render(<SingleLetterContent {...defaultProps} sanitizedHtml="" />);
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe('');
    });

    it('renders with long content', () => {
      const longHtml = '<p>'.concat('Very long content. '.repeat(100), '</p>');
      render(<SingleLetterContent {...defaultProps} sanitizedHtml={longHtml} />);
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe(longHtml);
    });
  });

  describe('Component Structure', () => {
    it('renders all required wrapper components', () => {
      render(<SingleLetterContent {...defaultProps} />);

      // Verify main container wrapper
      expect(screen.getByTestId(`${defaultProps.id}-container-panel-wrapper`)).toBeInTheDocument();

      // Verify content container
      expect(screen.getByTestId(`${defaultProps.testId}-container-body`)).toBeInTheDocument();

      // Verify inner wrapper
      expect(screen.getByTestId(`${defaultProps.id}-content-section-panel-wrapper`)).toBeInTheDocument();
    });

    it('renders content in correct order', () => {
      const { container } = render(<SingleLetterContent {...defaultProps} />);
      const logo = screen.getByTestId('dayforce-logo');
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);

      // Logo should appear before content in DOM
      expect(container.contains(logo)).toBeTruthy();
      expect(container.contains(content)).toBeTruthy();
      expect(logo.compareDocumentPosition(content)).toBe(Node.DOCUMENT_POSITION_FOLLOWING);
    });
  });

  describe('Props Handling', () => {
    it('handles missing testId prop', () => {
      const { id, sanitizedHtml } = defaultProps;
      render(<SingleLetterContent id={id} testId="" sanitizedHtml={sanitizedHtml} />);
      expect(screen.getByTestId(`${id}-container-panel-wrapper`)).toBeInTheDocument();
    });

    it('handles special characters in content', () => {
      const specialContent = '<p>&amp; &lt; &gt; " \'</p>';
      render(<SingleLetterContent {...defaultProps} sanitizedHtml={specialContent} />);
      const content = screen.getByTestId(`${defaultProps.testId}-letter-content`);
      expect(content.innerHTML).toBe(specialContent);
    });
  });
});
