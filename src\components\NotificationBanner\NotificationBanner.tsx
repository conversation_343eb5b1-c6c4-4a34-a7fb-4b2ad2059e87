import React from 'react';
import {
  NotificationBanner as EverestNotificationBanner,
  NotificationBannerBody,
  type INotificationBannerProps as IEverestNotificationBannerProps,
} from '@ceridianhcm/components';

interface INotificationBannerProps extends IEverestNotificationBannerProps {}

const NotificationBanner: React.FC<INotificationBannerProps> & {
  Body: typeof NotificationBannerBody;
} = ({ ...everestProps }) => {
  return <EverestNotificationBanner {...everestProps} />;
};

NotificationBanner.Body = NotificationBannerBody;

export { NotificationBanner, INotificationBannerProps };
